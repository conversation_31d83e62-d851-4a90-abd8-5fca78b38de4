
import React, { useState, useEffect } from 'react';
import {
    View,
    Image,
    ActivityIndicator,
    Text,
    Modal,
    useWindowDimensions,
    TouchableOpacity,
    TextInput,
    Dimensions,
    StyleSheet,
    ScrollView,
    FlatList,
} from 'react-native';
import Colors from '../../constant/Colors';
import { getCachedUrlContent, getImageFromFirebaseStorage, isMobile, signInWithPhoneForCRMUser, updateWebTokenAnonymous } from '../../util/commonFuncs';
import AntDesign from "react-native-vector-icons/AntDesign";
import { UserStore } from '../../store/userStore';
import { TempStore } from '../../store/tempStore';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { Collections } from '../../constant/firebase';
import { CommonStore } from '../../store/commonStore';
import ApiClient from '../../util/ApiClient';
import API from '../../constant/API';
import moment from 'moment';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ORDER_TYPE } from '../../constant/common';
import { apiUrl, prefix } from '../../constant/env';

import { useLinkTo } from "@react-navigation/native";
import AsyncImage from "../asyncImage";

import { v4 as uuidv4 } from "uuid";

const VOUCHER_PROMOTION_INFO_PAGE = {
    REGISTER_USER: 'REGISTER_USER',
    CART_INFO: 'CART_INFO',
};



// window.addEventListener('visibilitychange', onBeforeUnload);
// window.addEventListener('pagehide', onBeforeUnload);

var cooldownTimerTime = 10; // 5

const GeneralAskUserInfo = props => {
    const {
        linkToFunc,
    } = props;

    const linkTo = useLinkTo();

    const {
        width: windowWidth,
        height: windowHeight,
    } = useWindowDimensions();

    const [showVoucherPromotionInfoModal, setShowVoucherPromotionInfoModal] = useState(false);

    const [currVoucherPromotionInfoPage, setCurrVoucherPromotionInfoPage] = useState(VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER);

    const [verifySMSModal, setVerifySMSModal] = useState(false);
    const [verificationCode, setVerificationCode] = useState('');
    const [isVerified, setIsVerified] = useState(false); // herks test

    const [name, setName] = useState('');
    const [phone, setPhone] = useState('');
    const [birthday, setBirthday] = useState(moment(Date.now()));
    const [address, setAddress] = useState('');
    const [lat, setLat] = useState(0);
    const [lng, setLng] = useState(0);

    const [sentCode, setSentCode] = useState(true);
    const [existingCustomer, setExistingCustomer] = useState(false); //For testing UI purpose

    const [outletId, setOutletId] = useState('');
    const [outletName, setOutletName] = useState('');
    const [outletCover, setOutletCover] = useState('');
    const [merchantId, setMerchantId] = useState('');
    const [merchantName, setMerchantName] = useState('');

    // const registerUserOrder = CommonStore.useState(s => s.registerUserOrder);
    const [registerUserOrder, setRegisterUserOrder] = useState({});

    const [cooldownTimer, setCooldownTimer] = useState(null);
    const [cooldownTimerTimeState, setCooldownTimerTimeState] = useState(cooldownTimerTime);
    const [cooldownActive, setCooldownActive] = useState(true);

    const isLoading = CommonStore.useState(s => s.isLoading);

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
    const selectedMerchant = CommonStore.useState(s => s.selectedMerchant);

    const showVoucherInfo = TempStore.useState(s => s.showVoucherInfo);
    const showGeneralAskUserInfo = TempStore.useState(s => s.showGeneralAskUserInfo);
    const isPaidFirstOrder = TempStore.useState(s => s.isPaidFirstOrder);

    const cartItems = TempStore.useState(s => s.cartItemsT);
    const cartItemsProcessed = TempStore.useState(s => s.cartItemsProcessedT);
    const cartOutletId = TempStore.useState(s => s.cartOutletIdT);

    const showClaimVoucher = TempStore.useState(s => s.showClaimVoucher);
    const claimingVoucher = TempStore.useState(s => s.claimingVoucher);
    const selectedClaimVoucher = TempStore.useState(s => s.selectedClaimVoucher);

    const email = UserStore.useState((s) => s.email);
    const userPointsBalance = UserStore.useState(s => s.userPointsBalance);

    // const userEmail = UserStore.useState((s) => s.email);
    const userName = UserStore.useState((s) => s.name);
    const userNumber = UserStore.useState((s) => s.number);

    const userIdAnonymous = UserStore.useState(s => s.userIdAnonymous);

    useEffect(() => {
        if (userName && userNumber) {
            setName(userName);
            setPhone(userNumber);
            setIsVerified(true);
        }
    }, [userName, userNumber]);

    useEffect(() => {
        if (selectedOutlet && selectedOutlet.uniqueId && selectedMerchant && selectedMerchant.uniqueId) {
            setOutletId(selectedOutlet.uniqueId);
            setOutletName(selectedOutlet.name);
            setOutletCover(selectedOutlet.cover);
            setMerchantId(selectedOutlet.merchantId);
            setMerchantName(selectedMerchant.name);
        }
    }, [selectedOutlet, selectedMerchant]);

    // send a verification code to user phone number
    const sendVerifyOTP = async () => {
        var userPhone = phone.replaceAll('-', '');

        if (!userPhone) {
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Error",
            //         message: "Please key in your contact.",
            //     };
            // });
            window.confirm(
                "Please key in your phone number."
            );
            return;
        }
////
        if (userPhone && /\D/.test(userPhone)) {
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Error",
            //         message: "Only digits allowed for contact.",
            //     };
            // });
            window.confirm(
                "Only digits allowed for phone number."
            );
            return;
        }

        userPhone = userPhone.replace(/[^0-9]/g, '');

        if (userPhone && !(userPhone.length === 10 || userPhone.length === 11)) {
            if (
                (userPhone.startsWith('011') && userPhone.length === 11)
                ||
                (userPhone.startsWith('6011') && userPhone.length === 12)
            ) {
                // still valid, do nothing
            }
            else {
                setVerifySMSModal(false);
                // CommonStore.update((s) => {
                //     s.alertObj = {
                //         title: "Error",
                //         message: "Invalid contact format.\neg: 60123456789.",
                //     };
                // });
                window.confirm(
                    "Invalid phone number format.\neg: 60123456789."
                );
                return;
            }
        }

        if (!userPhone.startsWith('6')) {
            userPhone = '6' + userPhone;
        }

        const data = {
            phoneNumber: userPhone,
        };

        const response = await ApiClient.POST(API.sendOTPVerificationToSMSWeb, data);

        if (response) {
            // alert(`Success: Code has been sent to \n ${data.phoneNumber} !`);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Success",
            //         message: `Code has been sent to \n ${data.phoneNumber} !`,
            //     };
            // });
            window.confirm(
                `Code has been sent to ${data.phoneNumber}.`
            );
        } else {
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Failed",
            //         message: `Code failed to send to \n ${data.phoneNumber} !`,
            //     };
            // });
            window.confirm(
                `Please wait for 3 minutes before send again.`
            );
        }
    }

    // check the verification code user inputted
    const verifyRegisterOTP = async (state) => {
        if (!verificationCode || verificationCode.length !== 6) {
            window.confirm(
                `Empty or invalid code, please try again.`
            );
            return;
        }

        var userPhone = phone.replaceAll('-', '');

        userPhone = userPhone.replace(/[^0-9]/g, '');

        if (!userPhone.startsWith('6')) {
            userPhone = '6' + userPhone;
        }

        // const OTPSnapshot = await firebase.firestore().collection(Collections.OTPCache)
        //     .where('phoneNumber', '==', userPhone)
        //     .limit(1)
        //     .get();

        const OTPSnapshot = await getDocs(
            query(
                collection(global.db, Collections.OTPCache),
                where('phoneNumber', '==', userPhone),
                limit(1),
            )
        );

        var OTP = null;
        if (!OTPSnapshot.empty) {
            OTP = OTPSnapshot.docs[0].data();
        } else {
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Invalid code",
            //         message: `Please try again.`,
            //     };
            // });
            window.confirm(
                `Invalid code, please try again.`
            );
            return
        }

        if (OTP.code == verificationCode) {
            setIsVerified(true);
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Verification Success",
            //         message: `Please proceed to register !`,
            //     };
            // });
            window.confirm(
                `Verified successfully.`
            );
        }
    }

    const registerUser = async () => {
        // 2023-05-06 - No need verify first
        // if (!isVerified) {
        //     // CommonStore.update((s) => {
        //     //     s.alertObj = {
        //     //         title: "Error",
        //     //         message: "Please verify your phone number first.",
        //     //     };
        //     // });
        //     window.confirm(
        //         `Please verify your phone number first.`
        //     );
        //     return;
        // }

        var userPhone = phone.replaceAll('-', '').replaceAll(' ', '').replaceAll('+', '');

        userPhone = userPhone.replace(/[^0-9]/g, '');

        // if (userPhone.startsWith('6')) {
        //     userPhone = userPhone.slice(1);
        // }

        if (!userPhone.startsWith('6')) {
            userPhone = '6' + userPhone;
        }

        if (userPhone && name) {

        }
        else {
            window.confirm('Please fill in the name and phone number before proceed.');

            return;
        }

        if (userPhone && /\D/.test(userPhone)) {
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Error",
            //         message: "Only digits allowed for contact.",
            //     };
            // });
            window.confirm(
                "Only digits allowed for phone number."
            );
            return;
        }

        userPhone = userPhone.replace(/[^0-9]/g, '');

        if (userPhone && !(userPhone.length === 10 || userPhone.length === 11)) {
            if (
                (userPhone.startsWith('011') && userPhone.length === 11)
                ||
                (userPhone.startsWith('6011') && userPhone.length === 12)
            ) {
                // still valid, do nothing
            }
            else {
                window.confirm(
                    "Invalid phone number format.\neg: 60123456789."
                );
                return;
            }
        }

        // if (showVoucherInfo && showVoucherInfo.voucherQuantity > 0) {

        // }
        // else {
        //     window.confirm('This voucher is already fully redeemed.');

        //     return;
        // }

        // var userPhone = phone.replaceAll('-', '');

        // if (userPhone.startsWith('6')) {
        //     userPhone = userPhone.slice(1);
        // }

        const body = {
            userPhone: userPhone,
            outletId: outletId,
            merchantId: merchantId,
            merchantName: merchantName,
            outletName: registerUserOrder.outletName || outletName,
            merchantLogo: registerUserOrder.merchantLogo || '',
            outletCover: registerUserOrder.outletCover || '',
            userName: name,

            dob: moment(birthday).valueOf(),

            address: address,
            lat: lat,
            lng: lng,

            // userOrderId: (registerUserOrder.uniqueId && !registerUserOrder.isCashbackClaimed) ? registerUserOrder.uniqueId : null,
            // taggableVoucherId: (showVoucherInfo && showVoucherInfo.uniqueId) ? showVoucherInfo.uniqueId : null,
            taggableVoucherId: null,

            isVerified: isVerified,

            userIdAnonymous: userIdAnonymous,
            toConvertAnonymousPoints: true,

            tracking: '2',
        };

        CommonStore.update(s => {
            s.isLoading = true;
        });

        // const userSnapshot = await firebase.firestore()
        //     .collection(Collections.User)
        //     // .where('uniqueName', '==', uniqueName)
        //     .where('number', '==', userPhone)
        //     .limit(1)
        //     .get();

        // var findUser = null;
        // if (!userSnapshot.empty) {
        //     findUser = userSnapshot.docs[0].data();
        // }

        if (
            // !findUser
            true
        ) {
            ApiClient.POST(API.claimTaggableVoucherKweb, body).then(async (result) => {
                if (result && result.status === "success") {
                    // here can start proceed to next page, to show cart modal page (if applicable)                    

                    // CommonStore.update(s => {
                    //     s.registerUserOrder = result.userOrder || {};
                    // });

                    // CommonStore.update((s) => {
                    //     s.alertObj = {
                    //         title: "Success",
                    //         message: "Account creation success! Please check your phone for the password.",
                    //     };                        

                    //     // s.isAuthenticating = false;
                    // });

                    ////////////////////////////////////////////////////////////////////////////////

                    // write in the user name and phone first

                    await AsyncStorage.setItem('storedUserName', name);
                    await AsyncStorage.setItem('storedUserPhone', userPhone);

                    global.storedUserName = name;
                    global.storedUserPhone = userPhone;

                    ////////////////////////////////////////////////////////////////////////////////

                    // if (showVoucherInfo) {
                    //     window.confirm(
                    //         `${result.message}`
                    //     );
                    // }
                    // else if (showGeneralAskUserInfo) {
                    //     window.confirm(
                    //         `Subscribed successfully! We will update you if there are any upcoming promotions or vouchers.`
                    //     );
                    // }

                    ///////////////////////////////////////////////////////////

                    var userCrm = result.userCrm;
                    var userActual = result.userActual;
                    var userTaggableVoucher = result.data;
                    // var taggableVoucher = result.taggableVoucher;

                    ///////////////////////////////////////////////////////////

                    // determine the next flow:
                    // a. check if is voucher modal, and if claimed, if not claimed?
                    // b. check if is promo interested modal, can just 

                    ///////////////////////////////////////////////////////////

                    await signInWithPhoneForCRMUser(selectedOutlet);

                    updateWebTokenAnonymous();

                    ///////////////////////////////////////////////////////////

                    if (showGeneralAskUserInfo) {
                        TempStore.update(s => {
                            // s.showVoucherInfo = null;
                            s.showGeneralAskUserInfo = false;
                            // s.isPaidFirstOrder = false;

                            // s.cartItemsT = [];
                            // s.cartItemsProcessedT = [];

                            // s.cartOutletIdT = (selectedOutlet && selectedOutlet.uniqueId) ? selectedOutlet.uniqueId : null;
                        });

                        // setShowVoucherPromotionInfoModal(false);

                        setCurrVoucherPromotionInfoPage(VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER);

                        // clearInterval(cooldownTimer);
                        // cooldownTimerTime = 10;
                        // setCooldownTimerTimeState(cooldownTimerTime);
                        // setCooldownActive(true);

                        setTimeout(() => {
                            // if (global.applyForPromotionCaller && global.applyPromoCode) {
                            //     global.applyForPromotionCaller(global.applyPromoCode);                                

                            //     // setTimeout(() => {
                            //     //     global.applyForPromotionCaller = undefined;
                            //     // }, 1000);                                
                            // }

                            if (global.applyPromoCode) {
                                window.confirm('Verified successfully, kindly fill in the promo code to apply again.');

                                global.applyPromoCode = '';
                            }
                        }, 2000);
                    }
                }
                else {
                    // CommonStore.update((s) => {
                    //     s.alertObj = {
                    //         title: "Error",
                    //         message: "Failed to create the account.",
                    //     };
                    // });

                    if (showGeneralAskUserInfo) {
                        window.confirm(
                            `Unable to subscribe to the outlet for now, please try again later.`
                        );
                    }
                }

                CommonStore.update(s => {
                    s.isLoading = false;
                });
            });
        }
        else {
            // CommonStore.update(s => {
            //     s.isLoading = false;
            // });

            // setExistingCustomer(true);
        }
    };

    const regUserClaimVoucher = async () => {
        // 2023-05-06 - No need verify first
        // if (!isVerified) {
        //     // CommonStore.update((s) => {
        //     //     s.alertObj = {
        //     //         title: "Error",
        //     //         message: "Please verify your phone number first.",
        //     //     };
        //     // });
        //     window.confirm(
        //         `Please verify your phone number first.`
        //     );
        //     return;
        // }

        var userPhone = phone.replaceAll('-', '').replaceAll(' ', '').replaceAll('+', '');

        userPhone = userPhone.replace(/[^0-9]/g, '');

        // if (userPhone.startsWith('6')) {
        //     userPhone = userPhone.slice(1);
        // }

        if (!userPhone.startsWith('6')) {
            userPhone = '6' + userPhone;
        }

        if (userPhone && name) {

        }
        else {
            window.confirm('Please fill in the name and phone number before proceed.');

            return;
        }

        userPhone = userPhone.replace(/[^0-9]/g, '');

        if (userPhone && !(userPhone.length === 10 || userPhone.length === 11)) {
            if (
                (userPhone.startsWith('011') && userPhone.length === 11)
                ||
                (userPhone.startsWith('6011') && userPhone.length === 12)
            ) {
                // still valid, do nothing
            }
            else {
                window.confirm(
                    "Invalid phone number format.\neg: 60123456789."
                );
                return;
            }
        }

        // if (showVoucherInfo && showVoucherInfo.voucherQuantity > 0) {

        // }
        // else {
        //     window.confirm('This voucher is already fully redeemed.');

        //     return;
        // }

        // var userPhone = phone.replaceAll('-', '');

        // if (userPhone.startsWith('6')) {
        //     userPhone = userPhone.slice(1);
        // }

        const body = {
            userPhone: userPhone,
            outletId: outletId,
            merchantId: merchantId,
            merchantName: merchantName,
            outletName: registerUserOrder.outletName || outletName,
            merchantLogo: registerUserOrder.merchantLogo || '',
            outletCover: registerUserOrder.outletCover || '',
            userName: name,

            dob: moment(birthday).valueOf(),

            address: address,
            lat: lat,
            lng: lng,

            // userOrderId: (registerUserOrder.uniqueId && !registerUserOrder.isCashbackClaimed) ? registerUserOrder.uniqueId : null,
            // taggableVoucherId: (showVoucherInfo && showVoucherInfo.uniqueId) ? showVoucherInfo.uniqueId : null,
            taggableVoucherId: null,

            isVerified: isVerified,

            userIdAnonymous: userIdAnonymous,
            toConvertAnonymousPoints: true,

            tracking: '3',
        };

        CommonStore.update(s => {
            s.isLoading = true;
        });


        // if (claimingVoucher) {
        //     const userSnapshot = await getDocs(
        //         query(
        //             collection(global.db, Collections.User),
        //             where('number', '==', userPhone),
        //             limit(1),
        //         )
        //     );

        //     var findUser = null;
        //     if (!userSnapshot.empty) {
        //         findUser = userSnapshot.docs[0].data();
        //     }
        // }

        if (
            // !findUser
            true
        ) {
            ApiClient.POST(API.claimTaggableVoucherKweb, body).then(async (result) => {
                if (result && result.status === "success") {
                    // here can start proceed to next page, to show cart modal page (if applicable)                    

                    // CommonStore.update(s => {
                    //     s.registerUserOrder = result.userOrder || {};
                    // });

                    // CommonStore.update((s) => {
                    //     s.alertObj = {
                    //         title: "Success",
                    //         message: "Account creation success! Please check your phone for the password.",
                    //     };                        

                    //     // s.isAuthenticating = false;
                    // });

                    ////////////////////////////////////////////////////////////////////////////////

                    // write in the user name and phone first

                    await AsyncStorage.setItem('storedUserName', name);
                    await AsyncStorage.setItem('storedUserPhone', userPhone);

                    global.storedUserName = name;
                    global.storedUserPhone = userPhone;

                    ////////////////////////////////////////////////////////////////////////////////

                    // if (showVoucherInfo) {
                    //     window.confirm(
                    //         `${result.message}`
                    //     );
                    // }
                    // else if (showGeneralAskUserInfo) {
                    //     window.confirm(
                    //         `Subscribed successfully! We will update you if there are any upcoming promotions or vouchers.`
                    //     );
                    // }

                    ///////////////////////////////////////////////////////////

                    var userCrm = result.userCrm;
                    var userActual = result.userActual;
                    var userTaggableVoucher = result.data;
                    // var taggableVoucher = result.taggableVoucher;

                    ///////////////////////////////////////////////////////////

                    // determine the next flow:
                    // a. check if is voucher modal, and if claimed, if not claimed?
                    // b. check if is promo interested modal, can just 

                    ///////////////////////////////////////////////////////////

                    await signInWithPhoneForCRMUser(selectedOutlet);

                    updateWebTokenAnonymous();

                    ///////////////////////////////////////////////////////////

                    if (showGeneralAskUserInfo) {
                        TempStore.update(s => {
                            // s.showVoucherInfo = null;
                            s.showGeneralAskUserInfo = false;
                            // s.isPaidFirstOrder = false;

                            // s.cartItemsT = [];
                            // s.cartItemsProcessedT = [];

                            // s.cartOutletIdT = (selectedOutlet && selectedOutlet.uniqueId) ? selectedOutlet.uniqueId : null;
                        });

                        // setShowVoucherPromotionInfoModal(false);

                        setCurrVoucherPromotionInfoPage(VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER);

                        // clearInterval(cooldownTimer);
                        // cooldownTimerTime = 10;
                        // setCooldownTimerTimeState(cooldownTimerTime);
                        // setCooldownActive(true);
                    }
                }
                else {
                    // CommonStore.update((s) => {
                    //     s.alertObj = {
                    //         title: "Error",
                    //         message: "Failed to create the account.",
                    //     };
                    // });

                    if (showGeneralAskUserInfo) {
                        window.confirm(
                            `Unable to subscribe to the outlet for now, please try again later.`
                        );
                    }
                }

                CommonStore.update(s => {
                    s.isLoading = false;
                });
            });
        }
        else {
            CommonStore.update(s => {
                s.isLoading = false;
            });
        }
    };

    const claimVoucher = async () => {
        console.log('selectedClaimVoucher', selectedClaimVoucher)
        var userPhone = phone.replaceAll('-', '').replaceAll(' ', '').replaceAll('+', '');

        userPhone = userPhone.replace(/[^0-9]/g, '');

        if (!userPhone.startsWith('6')) {
            userPhone = '6' + userPhone;
        }

        if (userPhone && name) {

        }
        else {
            alert('Please fill in the name and phone number before proceed.');

            return;
        }

        const body = {
            userPhone: userPhone,
            outletId: outletId,
            merchantId: merchantId,
            merchantName: merchantName,
            outletName: selectedClaimVoucher.outletName || outletName,
            merchantLogo: selectedClaimVoucher.merchantLogo || '',
            outletCover: selectedClaimVoucher.outletCover || '',
            userName: name,

            dob: moment(birthday).valueOf(),
            email: email || '',

            address: address,
            lat: lat,
            lng: lng,

            taggableVoucherId: selectedClaimVoucher.uniqueId,

            voucherPoints: -selectedClaimVoucher.voucherPointsRequired,

            userIdAnonymous: userIdAnonymous,
            toConvertAnonymousPoints: true,

            // userOrderId: (registerUserOrder.uniqueId && !registerUserOrder.isCashbackClaimed) ? registerUserOrder.uniqueId : null,
        
            tracking: '4',
        };

        //////////////////////////////////////

        // check validity

        if (userPointsBalance < selectedClaimVoucher.voucherPointsRequired) {
            alert('Info\n\nInsufficient points to claim the voucher.');

            return;
        }

        if (selectedClaimVoucher && selectedClaimVoucher.voucherQuantity > 0) {

        }
        else {
            alert('Info\n\nThis voucher has been fully redeemed.');

            return;
        }

        CommonStore.update(s => {
            s.isLoading = true;
        });

        ApiClient.POST(API.claimTaggableVoucherKweb, body).then(async (result) => {
            if (result && result.status === "success") {
                // const voucherSnapshot = await firebase.firestore().collection(Collections.TaggableVoucher)
                //   .where('uniqueId', '==', claimTaggableVoucher.uniqueId)
                //   .limit(1)
                //   .get();

                // 2022-05-08 - No need first
                // const voucherSnapshot = await getDocs(
                //     query(
                //         collection(global.db, Collections.TaggableVoucher),
                //         where('uniqueId', '==', selectedClaimVoucher.uniqueId),
                //         limit(1),
                //     )
                // );

                // var voucher = null;

                // if (!voucherSnapshot.empty) {
                //     voucher = voucherSnapshot.docs[0].data();
                // }

                // if (voucher) {
                //     TempStore.update(s => {
                //         s.selectedClaimVoucher = voucher;
                //     });
                // }

                TempStore.update(s => {
                    s.selectedClaimVoucher = null;
                });

                alert(`${result.message}`);

            }
            else {
                TempStore.update(s => {
                    s.selectedClaimVoucher = null;
                });

                CommonStore.update((s) => {
                    s.alertObj = {
                        title: "Error",
                        message: "Unable to claim the voucher for now.",
                    };
                });
            }

            CommonStore.update(s => {
                s.isLoading = false;
            });
        });

        TempStore.update(s => {
            s.claimingVoucher = false;
            s.showGeneralAskUserInfo = false;
        });
    };

    /////////////////////////////////////////////////////////////////////////



    return (
        <>
            <Modal
                style={{ flex: 1 }}
                visible={showGeneralAskUserInfo}
                transparent={true}
                animationType="fade"
            >
                <View style={styles.modalContainer}>
                    <View
                        style={{
                            // width: isMobile() ? windowWidth * 0.8 : windowWidth * 0.3,
                            // height: isMobile() ? windowHeight * 0.4 : windowHeight * 0.35,
                            backgroundColor: Colors.whiteColor,
                            borderRadius: 8,
                            padding: isMobile()
                                ? windowWidth * 0.03
                                : windowWidth * 0.04,
                            alignItems: "center",
                            justifyContent: "space-between",
                            paddingTop: isMobile()
                                ? windowWidth * 0.05
                                : windowWidth * 0.03,
                            paddingBottom: isMobile()
                                ? windowWidth * 0.04
                                : windowWidth * 0.02,
                            ...isMobile() && {
                                width: '90%',
                            }
                        }}
                    >
                        <TouchableOpacity
                            style={[
                                styles.closeButton,
                                {
                                    right: isMobile()
                                        ? windowWidth * 0.04
                                        : windowWidth * 0.01,
                                    top: isMobile()
                                        ? windowWidth * 0.04
                                        : windowWidth * 0.01,
                                },
                            ]}
                            onPress={async () => {
                                // setShowUserInfoModal(false);

                                // if want to hide, just place the order

                                // setShowVoucherPromotionInfoModal(false);

                                TempStore.update(s => {
                                    s.showGeneralAskUserInfo = false;
                                });
                            }}
                        >
                            <AntDesign
                                name="closecircle"
                                size={25}
                                color={Colors.fieldtTxtColor}
                            />

                            {/* <SimpleLineIcons name="close" size={25}
                                    color={Colors.fieldtTxtColor}
                                /> */}
                        </TouchableOpacity>

                        {
                            currVoucherPromotionInfoPage === VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER
                                ?
                                <>
                                    <View
                                        style={[
                                            styles.modalTitle,
                                            {
                                                // backgroundColor: 'red'
                                            },
                                            isMobile ? {
                                                marginTop: 25,
                                            } : {

                                            },
                                        ]}
                                    >
                                        <Text style={[styles.modalTitleText, {
                                            textAlign: 'center',
                                            marginTop: isMobile() ? 0 : 10,
                                        }, isMobile() ? {
                                            // width: '80%',
                                            // backgroundColor: 'red',
                                        } : {

                                        }]}>
                                            {
                                                (showClaimVoucher || (selectedClaimVoucher && selectedClaimVoucher.uniqueId))
                                                    ?
                                                    'Enter your name & number to claim your voucher!'
                                                    :
                                                    showGeneralAskUserInfo
                                                        ?
                                                        'Enter your name & number to enjoy the promotions!'
                                                        // 'Register Now to Get More\nVoucher/Promotion!'
                                                        :
                                                        ''
                                            }
                                        </Text>
                                    </View>

                                    <View
                                        style={[
                                            styles.modalBody,
                                            {
                                                width: "95%",
                                                alignItems: "center",
                                                // alignItems: 'flex-start',
                                                justifyContent: "flex-start",
                                                // backgroundColor: 'blue',

                                                marginTop: 10,
                                            },
                                        ]}
                                    >
                                        <View
                                            style={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                justifyContent: "space-between",
                                                width: "100%",
                                                // backgroundColor: 'green',
                                            }}
                                        >
                                            <Text
                                                style={[
                                                    styles.modalBodyText,
                                                    { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                ]}
                                            >
                                                Name
                                            </Text>

                                            <View style={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                justifyContent: "space-between",
                                                width: "80%",
                                                // backgroundColor: 'green',
                                            }}>
                                                <TextInput
                                                    style={[
                                                        styles.textInput,
                                                        {
                                                            // marginHorizontal: 24,
                                                            width: "100%",
                                                        },
                                                    ]}
                                                    editable={!email}
                                                    multiline={false}
                                                    clearButtonMode="while-editing"
                                                    placeholder="John Smith"
                                                    onChangeText={(text) => {
                                                        // setUserInfoName(text)

                                                        // UserStore.update((s) => {
                                                        //     s.userInfoName = text;
                                                        // });
                                                        setName(text);
                                                    }}
                                                    onFocus={() => {
                                                        setCooldownActive(false);
                                                    }}
                                                    value={name}
                                                />
                                            </View>
                                        </View>

                                        <View
                                            style={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                justifyContent: "space-between",
                                                width: "100%",
                                                // backgroundColor: 'green',
                                            }}
                                        >
                                            <Text
                                                style={[
                                                    styles.modalBodyText,
                                                    { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                ]}
                                            >
                                                Phone
                                            </Text>

                                            <View style={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                justifyContent: "space-between",
                                                width: "80%",
                                                // backgroundColor: 'green',
                                            }}>
                                                <TextInput
                                                    editable={!isVerified && !email}
                                                    style={[
                                                        styles.textInput,
                                                        {
                                                            // marginHorizontal: 24,
                                                            width: "75%",
                                                            ...isVerified && {
                                                                color: Colors.fieldtTxtColor,
                                                            }
                                                        },
                                                    ]}
                                                    multiline={false}
                                                    clearButtonMode="while-editing"
                                                    placeholder="60123456789"
                                                    onChangeText={(text) => {
                                                        // setUserInfoPhone(text)

                                                        // UserStore.update((s) => {
                                                        //     s.userInfoPhone = text;
                                                        // });

                                                        setPhone(text);
                                                    }}
                                                    onFocus={() => {
                                                        setCooldownActive(false);
                                                    }}
                                                    value={phone}
                                                    keyboardType={"decimal-pad"}
                                                />

                                                <TouchableOpacity
                                                    disabled={true}
                                                    onPress={async () => {
                                                        if (isVerified) {
                                                            window.confirm(
                                                                "This phone number is already verified."
                                                            );
                                                        }
                                                        else {
                                                            var userPhone = phone.replaceAll('-', '');

                                                            if (!userPhone) {
                                                                setVerifySMSModal(false);
                                                                // CommonStore.update((s) => {
                                                                //     s.alertObj = {
                                                                //         title: "Error",
                                                                //         message: "Please key in your contact.",
                                                                //     };
                                                                // });

                                                                window.confirm(
                                                                    "Please key in your phone number."
                                                                );
                                                                return;
                                                            }

                                                            if (userPhone && /\D/.test(userPhone)) {
                                                                setVerifySMSModal(false);
                                                                // CommonStore.update((s) => {
                                                                //     s.alertObj = {
                                                                //         title: "Error",
                                                                //         message: "Only digits allowed for contact.",
                                                                //     };
                                                                // });
                                                                window.confirm(
                                                                    "Only digits allowed for phone number."
                                                                );
                                                                return;
                                                            }

                                                            userPhone = userPhone.replace(/[^0-9]/g, '');

                                                            if (userPhone && !(userPhone.length === 10 || userPhone.length === 11)) {
                                                                if (
                                                                    (userPhone.startsWith('011') && userPhone.length === 11)
                                                                    ||
                                                                    (userPhone.startsWith('6011') && userPhone.length === 12)
                                                                ) {
                                                                    // still valid, do nothing
                                                                }
                                                                else {
                                                                    setVerifySMSModal(false);
                                                                    // CommonStore.update((s) => {
                                                                    //     s.alertObj = {
                                                                    //         title: "Error",
                                                                    //         message: "Invalid contact format.\neg: 60123456789.",
                                                                    //     };
                                                                    // });
                                                                    window.confirm(
                                                                        "Invalid phone number format.\neg: 60123456789."
                                                                    );
                                                                    return;
                                                                }
                                                            }

                                                            const lastSentTime = await AsyncStorage.getItem('lastSentTime');

                                                            var isValid = false;

                                                            if (lastSentTime) {
                                                                const lastSentTimeParsed = moment(parseInt(lastSentTime)).valueOf();

                                                                if (moment().diff(lastSentTimeParsed, 'minute') >= 3) {
                                                                    isValid = true;
                                                                }
                                                            }
                                                            else {
                                                                // means new one

                                                                isValid = true;
                                                            }

                                                            if (isValid) {
                                                                setSentCode(false);
                                                            }

                                                            setVerifySMSModal(true);
                                                        }
                                                    }}
                                                    style={{
                                                        backgroundColor:
                                                            Colors.primaryColor,
                                                        alignSelf: "center",
                                                        alignItems: 'center',
                                                        // width: isMobile() ? windowWidth * 0.2 : 110,
                                                        paddingHorizontal: 20,
                                                        height: isMobile() ? 35 : 40,
                                                        borderRadius: 10,
                                                        justifyContent: "center",

                                                        marginLeft: 20,

                                                        opacity: 0,
                                                    }}
                                                >
                                                    <Text
                                                        style={{
                                                            alignSelf: 'center',
                                                            color: Colors.whiteColor,
                                                            fontFamily: 'NunitoSans-Bold',
                                                            fontSize: 14

                                                        }}>
                                                        VERIFY
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                    </View>

                                    <View
                                        style={{
                                            alignItems: "center",
                                            flexDirection: "row",
                                            // justifyContent: 'space-between',
                                            justifyContent: "space-around",
                                            width: "100%",
                                        }}
                                    >
                                        {/* <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={() => {
                  setInputTablePax(selectedOutletTable.capacity);
                  setUpdateTableModal(true);
                }}>
                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Edit</Text>
              </TouchableOpacity> */}

                                        <TouchableOpacity
                                            disabled={isLoading}
                                            style={[
                                                styles.modalSaveButton,
                                                {
                                                    // width: isMobile() ? windowWidth * 0.3 : windowWidth * 0.1,
                                                    marginTop: windowHeight * 0.04,
                                                    paddingHorizontal: windowWidth * 0.05,

                                                    // width: windowWidth * 0.01,
                                                    width: '40%',
                                                },
                                            ]}
                                            onPress={() => {
                                                //proceedOrderAfterUserInfo();
                                                if (showClaimVoucher || (selectedClaimVoucher && selectedClaimVoucher.uniqueId)) {
                                                    // regUserClaimVoucher();
                                                    claimVoucher();
                                                }
                                                else {
                                                    registerUser();
                                                }
                                            }}
                                        >
                                            <Text
                                                style={[
                                                    styles.modalDescText,
                                                    { color: Colors.primaryColor },
                                                ]}
                                            >
                                                {isLoading ? 'LOADING...' : 'PROCEED'}
                                            </Text>
                                        </TouchableOpacity>

                                        {/* {
                (selectedOutlet && selectedOutlet.skipUserInfo)
                  ?
                  <TouchableOpacity
                    style={[
                      styles.modalSaveButton,
                      {
                        // width: isMobile() ? windowWidth * 0.3 : windowWidth * 0.1,
                        marginTop: windowHeight * 0.04,
                        paddingHorizontal: windowWidth * 0.05,

                        // width: windowWidth * 0.01,
                        width: '40%',
                      },
                    ]}
                    onPress={() => {
                      proceedOrderAfterUserInfo(true);
                    }}
                  >
                    <Text
                      style={[
                        styles.modalDescText,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      {'Skip'}
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
              } */}
                                    </View>
                                </>
                                :
                                <></>
                        }
                    </View>
                </View>
            </Modal>

            <Modal
                visible={verifySMSModal}
                transparent={true}
                supportedOrientations={['portrait', 'landscape']}
                animationType="fade"
                onRequestClose={() => { setVerifySMSModal(false) }}
            >
                <View style={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    height: windowHeight,
                    justifyContent: 'center',
                }}>
                    <View style={{
                        alignSelf: 'center',
                        width: isMobile() ? '80%' : (windowWidth * 0.5),
                        height: 320,
                        backgroundColor: 'white',

                        borderRadius: 10,
                        borderColor: '#E5E5E5',

                        shadowOffset: {
                            width: 0,
                            height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,

                        marginTop: 40,
                    }}>
                        <TouchableOpacity
                            onPress={() => {
                                setVerifySMSModal(false);
                            }}
                        >
                            <View style={{
                                marginTop: 10,
                                marginRight: 10,
                                alignSelf: 'flex-end',
                                height: 20,
                            }}>
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </View>
                        </TouchableOpacity>
                        <Text style={{
                            fontFamily: 'NunitoSans-Bold',
                            color: 'black',
                            fontSize: 20,
                            textAlign: 'center',
                            marginTop: 0,
                            // width: '100%',
                        }}>
                            SMS Verification
                        </Text>
                        <View style={{
                            width: '80%',
                            alignSelf: 'center',
                            marginTop: 20,
                        }}>
                            {sentCode == true ?
                                <Text
                                    style={{
                                        alignSelf: 'center',
                                        //color: Colors.whiteColor,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: 16,
                                        textAlign: 'center',
                                    }}
                                >
                                    Verification code has been sent to <b>+6{phone}</b>, please check your device if you have received the code, or wait for 3 minutes to send a new code.
                                </Text>
                                :
                                <Text
                                    style={{
                                        alignSelf: 'center',
                                        //color: Colors.whiteColor,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: 16
                                    }}
                                >
                                    Click <b>SEND</b> to send verification code to your device.
                                </Text>
                            }
                        </View>
                        <View
                            style={{
                                flexDirection: 'row',
                                alignSelf: 'center',
                            }}>
                            <View style={{
                                //justifyContent: 'center',
                                alignItems: 'center',
                                flexDirection: 'row',
                                marginTop: isMobile() ? '10%' : '15%',
                                //marginLeft: '-1%',
                                //width: isMobile() ? windowWidth : windowWidth * 1,
                                //backgroundColor: 'red'
                            }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: 'black',
                                    fontSize: 16,
                                    //textAlign: 'center',
                                    marginTop: 0,
                                    marginLeft: 5,
                                    // width: '100%',
                                }}>
                                    Code:
                                </Text>
                                <TextInput
                                    underlineColorAndroid={Colors.fieldtBgColor}
                                    clearButtonMode="while-editing"
                                    style={{
                                        height: 40,
                                        width: '60%',
                                        maxWidth: 150,
                                        paddingHorizontal: 20,
                                        backgroundColor: Colors.fieldtBgColor,
                                        paddingLeft: 15,
                                        marginLeft: 10,
                                        fontSize: 16,
                                        fontFamily: 'NunitoSans-Regular',
                                        textAlignVertical: 'center',
                                        alignItems: 'center',
                                        alignContent: 'center',

                                        borderRadius: 10,
                                        borderColor: '#E5E5E5',
                                        borderWidth: 1,
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                    }}
                                    placeholder="123456"
                                    placeholderTextColor={Colors.descriptionColor}
                                    keyboardType="decimal-pad"
                                    onChangeText={(text) => {
                                        setVerificationCode(text);
                                    }}
                                    value={verificationCode}
                                    clearTextOnFocus={true}
                                />
                                {sentCode == false ?
                                    <TouchableOpacity
                                        style={{
                                            backgroundColor:
                                                Colors.primaryColor,
                                            width: isMobile() ? windowWidth * 0.17 : 110,
                                            height: isMobile() ? 35 : 40,
                                            // height: 60,
                                            alignSelf: "center",
                                            alignItems: 'center',
                                            borderRadius: 10,
                                            justifyContent: "center",
                                            marginLeft: 20,
                                        }}
                                        onPress={async () => {
                                            const lastSentTime = await AsyncStorage.getItem('lastSentTime');

                                            var isValid = false;

                                            if (lastSentTime) {
                                                const lastSentTimeParsed = moment(parseInt(lastSentTime)).valueOf();

                                                if (moment().diff(lastSentTimeParsed, 'minute') >= 3) {
                                                    isValid = true;
                                                }
                                            }
                                            else {
                                                // means new one

                                                isValid = true;
                                            }

                                            if (isValid) {
                                                setSentCode(true);
                                                sendVerifyOTP();
                                            }
                                        }}>
                                        <Text
                                            style={{
                                                color: '#ffffff',
                                                fontSize: 14,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            SEND
                                        </Text>
                                    </TouchableOpacity>
                                    :
                                    <></>
                                }

                            </View>
                        </View>
                        <View
                            style={{
                                flexDirection: 'row',
                                alignSelf: 'center',
                                marginTop: '5%',
                            }}>
                            <TouchableOpacity
                                style={{
                                    backgroundColor:
                                        Colors.primaryColor,
                                    alignSelf: "center",
                                    alignItems: 'center',
                                    // width: isMobile() ? windowWidth * 0.2 : 110,
                                    paddingHorizontal: 20,
                                    height: isMobile() ? 35 : 40,
                                    borderRadius: 10,
                                    justifyContent: "center",
                                    ...isMobile() && {
                                        marginTop: 10,
                                    },
                                }}
                                onPress={() => verifyRegisterOTP()}>
                                <Text
                                    style={{
                                        color: '#ffffff',
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    VERIFY
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>



        </>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: "center",
        justifyContent: "center",
    },
    modalView: {
        height: Dimensions.get("window").width * 1,
        width: Dimensions.get("window").width * 0.8,
        backgroundColor: Colors.whiteColor,
        borderRadius: Dimensions.get("window").width * 0.07,
        padding: Dimensions.get("window").width * 0.07,
        alignItems: "center",
        justifyContent: "space-between",
    },
    closeButton: {
        position: "absolute",
        right: isMobile()
            ? Dimensions.get("window").width * 0.04
            : Dimensions.get("window").width * 0.01,
        top: isMobile()
            ? Dimensions.get("window").width * 0.04
            : Dimensions.get("window").width * 0.01,
    },
    modalTitle: {
        alignItems: "center",
    },
    modalBody: {
        flex: 0.8,
        alignItems: "center",
        justifyContent: "center",
    },
    modalTitleText: {
        fontFamily: "NunitoSans-Bold",
        // marginBottom: 10,
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 16,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        // flex: 1,
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 16,
        width: "20%",
    },
    modalSaveButton: {
        width: isMobile()
            ? Dimensions.get("window").width * 0.3
            : Dimensions.get("window").width * 0.1,
        backgroundColor: Colors.fieldtBgColor,
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 10,
        marginBottom: 10,

        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 2,
    },
});

export default GeneralAskUserInfo;