import React, { Component, useState, useEffect, useRef, useCallback } from "react";
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TouchableOpacity,
    Alert,
    Dimensions,
    Modal,
    ActivityIndicator,
    FlatList,
    BackHandler,
    useWindowDimensions,
    InteractionManager
} from "react-native";
import Colors from "../../constant/Colors";
import Styles from "../../constant/Styles";
import ApiClient from "../../util/ApiClient";
import API from "../../constant/API";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import Entypo from "react-native-vector-icons/Entypo";
import Ionicons from "react-native-vector-icons/Ionicons";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import * as Cart from "../../util/Cart";
import Draggable from "react-native-draggable";
import Back from "react-native-vector-icons/EvilIcons";
// import { FlatList } from 'react-native-gesture-handler';
// import StickyParallaxHeader from 'react-native-sticky-parallax-header'
import Close from "react-native-vector-icons/AntDesign";
import { CommonStore } from "../../store/commonStore";
import {
    APP_TYPE,
    CHARGES_TYPE,
    ORDER_TYPE,
    ORDER_TYPE_SUB,
    PRODUCT_PRICE_TYPE,
    UNIT_TYPE,
    UNIT_TYPE_SHORT,
    UPSELLING_SECTION,
    UPSELL_BY_TYPE,
    USER_ORDER_STATUS,
    WEB_ORDER_VARIANT_LAYOUT,
} from "../../constant/common";
import { UserStore } from "../../store/userStore";
import AsyncStorage from "@react-native-async-storage/async-storage";
import AsyncImage from "../asyncImage";
import { prefix } from "../../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { DataStore } from "../../store/dataStore";
import { checkIfVisibleItem, isMobile, isTestingOutlet } from "../../util/commonFuncs";
import { TempStore } from "../../store/tempStore";
import moment from "moment";
import { TextInput } from "react-native-web";
import { Collections } from "../../constant/firebase";
import Feather from 'react-native-vector-icons/Feather';
import RecommendedItems from "../recommendedItems";
import { TableStore } from "../../store/tableStore";
import { ReactComponent as DefaultImage } from '../../svg/DefaultImage.svg';

import loadable from '@loadable/component';
import Checkbox from 'rc-checkbox';
import { PaymentStore } from "../../store/paymentStore";
// const CheckboxLoadable = loadable.lib(() => import("rc-checkbox"));

import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { ANALYTICS, ANALYTICS_PARSED } from "../../constant/analytics";
import { logEvent } from "firebase/analytics";
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import s from "react-multiple-select-dropdown-lite";
import { idbDel, idbSet, safelyExecuteIdb } from "../../util/db";

import Toastify from 'toastify-js';
import "toastify-js/src/toastify.css";

global.addOnPosYByIdDict = {};

const MenuItemDetailModal = props => {

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();
    const linkTo = useLinkTo();

    const insets = useSafeAreaInsets();

    const addOnScrollViewRef = useRef(null);

    const [addOnVerifiedResultDict, setAddOnVerifiedResultDict] = useState({});

    const orderType = CommonStore.useState((s) => s.orderType);

    const outletCustomTaxList = CommonStore.useState((s) => s.outletCustomTaxList);

    const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);
    const selectedOutletItemCategoriesDict = CommonStore.useState((s) => s.selectedOutletItemCategoriesDict);
    const overrideItemPriceSkuDict = CommonStore.useState((s) => s.overrideItemPriceSkuDict);
    const overrideCategoryPriceNameDict = CommonStore.useState((s) => s.overrideCategoryPriceNameDict);
    const outletsItemAddOnDict = CommonStore.useState(s => s.outletsItemAddOnDict);
    const outletsItemAddOnChoiceDict = CommonStore.useState(s => s.outletsItemAddOnChoiceDict);
    const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);
    const selectedOutletItemAddOn = CommonStore.useState(s => s.selectedOutletItemAddOn);
    const selectedOutletItemAddOnOi = CommonStore.useState(s => s.selectedOutletItemAddOnOi);
    const selectedOutletItemAddOnChoice = CommonStore.useState(s => s.selectedOutletItemAddOnChoice);
    const onUpdatingCartItem = CommonStore.useState(s => s.onUpdatingCartItem);
    const cartItemChoices = CommonStore.useState(s => s.cartItemChoices);
    const isOrdering = CommonStore.useState((s) => s.isOrdering);
    const selectedOutletTableId = CommonStore.useState(s => s.selectedOutletTableId);
    const selectedOutletTablePax = CommonStore.useState(s => s.selectedOutletTablePax);
    const selectedOutletWaiterId = CommonStore.useState(s => s.selectedOutletWaiterId);
    const isLoading = CommonStore.useState((s) => s.isLoading);
    const firebaseUid = UserStore.useState((s) => s.firebaseUid);
    const cartItems = CommonStore.useState((s) => s.cartItems);
    const menuItemDetailModal = CommonStore.useState((s) => s.menuItemDetailModal);

    // PLACEHOLDER
    const [isMaxAddonsTest, setisMaxAddonsTest] = useState(false)
    //

    const [modalQuantity, setModalQuantity] = useState(1)
    const [totalPrice, setTotalPrice] = useState(0);
    const [addOnPrice, setAddOnPrice] = useState(0);
    const [newSelectedOutletItemAddOn, setNewSelectedOutletItemAddOn] = useState({});
    const [newSelectedOutletItemAddOnDetails, setNewSelectedOutletItemAddOnDetails] = useState({});
    const [variablePrice, setVariablePrice] = useState('0.00');
    const [addOnVerified, setAddOnVerified] = useState(false);
    const [addOnVerifiedResultList, setAddOnVerifiedResultList] = useState([]);
    const [remark, setRemark] = useState('');

    const [effectiveDays, setEffectiveDays] = useState(moment().day());

    const availableUpsellingCampaigns = CommonStore.useState(s => s.availableUpsellingCampaigns);

    const selectedOutletItemsRaw = CommonStore.useState(
        (s) => s.selectedOutletItems.filter(
            (item) =>
                item.priceType === undefined ||
                item.priceType === PRODUCT_PRICE_TYPE.FIXED ||
                item.priceType === PRODUCT_PRICE_TYPE.UNIT
        )
    );

    const currCrmUser = CommonStore.useState(s => s.currCrmUser);
    const selectedOutletCRMTagsDict = CommonStore.useState(s => s.selectedOutletCRMTagsDict);

    const upsellingCampaignsAfterCart = DataStore.useState(s => s.upsellingCampaignsAfterCart);

    // useFocusEffect(
    //     useCallback(() => {
    //         CommonStore.update(s => {
    //             s.currPageIframe = 'MenuItemDetails';
    //         });
    //     }, [])
    // );

    // useEffect(() => {
    //     if (menuItemDetailModal) {
    //         CommonStore.update(s => {
    //             s.currPageIframe = 'MenuItemDetails';
    //         });
    //     }
    // }, [menuItemDetailModal]);

    //////////////////////////////////////////////////////////////////////////////////////

    // 2024-12-13 - add on group's min and max choices support

    const selectedAddOnIdForChoiceQtyDict = CommonStore.useState(s => s.selectedAddOnIdForChoiceQtyDict);

    //////////////////////////////////////////////////////////////////////////////////////

    useEffect(() => {
        if (!global.isFromRecommendedItems) {
            // only recommend item if from normal item

            var upsellingCampaignsTemp = [];
            var toRecommendedItemsTemp = [];
            const availableUpsellingCampaignsFiltered = availableUpsellingCampaigns.filter(campaign => campaign.upsellingSection === UPSELLING_SECTION.AFTER_CART);

            if (selectedOutletItem && selectedOutletItem.uniqueId && availableUpsellingCampaignsFiltered.length > 0) {
                for (var campaignIndex = 0; campaignIndex < availableUpsellingCampaignsFiltered.length; campaignIndex++) {
                    var isValidCampaign = false;

                    const upsellingCampaign = availableUpsellingCampaignsFiltered[campaignIndex];

                    // setCurrUpsellingCampaign(currUpsellingCampaign);

                    const upsellingProductList = upsellingCampaign.productList;
                    const upsellingProductIdList = upsellingCampaign.productList.map(product => product.productId);

                    for (var upsellingIndex = 0; upsellingIndex < upsellingProductIdList.length; upsellingIndex++) {
                        for (var i = 0; i < selectedOutletItemsRaw.length; i++) {
                            if (upsellingProductIdList[upsellingIndex] === selectedOutletItemsRaw[i].uniqueId) {
                                if (checkIfVisibleItem(selectedOutletItemsRaw[i])) {
                                    const upsellingItem = upsellingProductList.find(product => product.productId === selectedOutletItemsRaw[i].uniqueId);
                                    const priceUpselling = upsellingItem.upsellPrice;

                                    var isValidOrderType = selectedOutletItemsRaw[i].hideInOrderTypes && selectedOutletItemsRaw[i].hideInOrderTypes.length > 0
                                        ? (selectedOutletItemsRaw[i].hideInOrderTypes.includes(orderType)
                                            ? false
                                            : true)
                                        : true;

                                    var isValidActive = (
                                        selectedOutletItemsRaw[i].isActive &&
                                        (selectedOutletItemsRaw[i].isAvailableDayActive
                                            ? (selectedOutletItemsRaw[i].effectiveTypeOptions.includes(effectiveDays) &&
                                                selectedOutletItemsRaw[i].effectiveStartTime && selectedOutletItemsRaw[i].effectiveEndTime &&
                                                moment().isSameOrAfter(
                                                    moment(selectedOutletItemsRaw[i].effectiveStartTime)
                                                        .year(moment().year())
                                                        .month(moment().month())
                                                        .date(moment().date())
                                                )
                                                &&
                                                moment().isBefore
                                                    (moment(selectedOutletItemsRaw[i].effectiveEndTime)
                                                        .year(moment().year())
                                                        .month(moment().month())
                                                        .date(moment().date())
                                                    ))
                                            : true) &&
                                        (selectedOutletItemsRaw[i].isOnlineMenu !== undefined ? selectedOutletItemsRaw[i].isOnlineMenu : true) &&
                                        (selectedOutletItemsRaw[i].isStockCountActive !== undefined &&
                                            selectedOutletItemsRaw[i].isStockCountActive !== false &&
                                            selectedOutletItemsRaw[i].stockCount !== undefined &&
                                            selectedOutletItemsRaw[i].toSellIgnoreStock !== undefined
                                            ? selectedOutletItemsRaw[i].isStockCountActive &&
                                            selectedOutletItemsRaw[i].stockCount > 0 &&
                                            (selectedOutletItemsRaw[i].toSellIgnoreStock !== undefined
                                                ? selectedOutletItemsRaw[i].toSellIgnoreStock
                                                : true)
                                            : true)
                                    );

                                    if (isValidOrderType && isValidActive) {
                                        // var existingItem = cartItemsProcessed.find(item => item.itemId === selectedOutletItemsRaw[i].uniqueId);
                                        var existingItem = null;
                                        if (selectedOutletItemsRaw[i].uniqueId === selectedOutletItem.uniqueId) {
                                            existingItem = selectedOutletItem;
                                        }

                                        if (!existingItem) {
                                            // didn't existed in cart, continue

                                            ////////////////////////////////////////////////////////////////

                                            // by customer tags

                                            if (upsellingItem.upsellByType === UPSELL_BY_TYPE.CUSTOMER) {
                                                if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                                                    // try compare with tags

                                                    if (currCrmUser && currCrmUser.uniqueId) {
                                                        if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                                                            for (var j = 0; j < upsellingItem.tagIdList.length; j++) {
                                                                var userTagId = upsellingItem.tagIdList[j];

                                                                if (selectedOutletCRMTagsDict[userTagId] && selectedOutletCRMTagsDict[userTagId].uniqueId) {
                                                                    var userTag = selectedOutletCRMTagsDict[userTagId];

                                                                    if (
                                                                        (userTag.emailList && userTag.emailList.includes(currCrmUser.userEmail))
                                                                        ||
                                                                        (userTag.phoneList && userTag.phoneList.includes(currCrmUser.userNumber))
                                                                    ) {
                                                                        // toRecommendedItemsTemp.push(selectedOutletItemsRaw[i]);

                                                                        // toRecommendedItemsTemp.push({
                                                                        //     ...selectedOutletItemsRaw[i],
                                                                        //     price: priceUpselling,

                                                                        //     priceUpselling: priceUpselling,
                                                                        //     upsellingCampaignId: upsellingCampaign.uniqueId,
                                                                        // });

                                                                        isValidCampaign = true;

                                                                        break;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                else {
                                                    if (
                                                        // toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)
                                                        upsellingCampaignsTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                                                    ) {
                                                        // means in the recommended list already, no need add
                                                    }
                                                    else {
                                                        // toRecommendedItemsTemp.push({
                                                        //     ...selectedOutletItemsRaw[i],
                                                        //     price: priceUpselling,

                                                        //     priceUpselling: priceUpselling,
                                                        //     upsellingCampaignId: upsellingCampaign.uniqueId,
                                                        // });

                                                        isValidCampaign = true;
                                                    }
                                                }
                                            }
                                            else if (upsellingItem.upsellByType === UPSELL_BY_TYPE.ORDER_ITEM || upsellingItem.upsellByType === undefined) {
                                                if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                                                    // try compare with tags

                                                    if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                                                        // var cartOutletItemList = cartItemsProcessed.map(item => {
                                                        //     var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                                                        //     return outletItem;
                                                        // });

                                                        var cartOutletItemList = [selectedOutletItem];

                                                        //////////////////////////

                                                        // 2024-03-01 - crash prevention

                                                        if (cartOutletItemList.find(findItem => findItem === undefined)) {
                                                            continue;
                                                        }

                                                        //////////////////////////

                                                        var isValid = false;

                                                        for (var cartItemIndex = 0; cartItemIndex < cartOutletItemList.length; cartItemIndex++) {
                                                            if (cartOutletItemList[cartItemIndex] && cartOutletItemList[cartItemIndex].crmUserTagIdList && cartOutletItemList[cartItemIndex].crmUserTagIdList.length > 0) {
                                                                for (var j = 0; j < cartOutletItemList[cartItemIndex].crmUserTagIdList.length; j++) {
                                                                    var productTagId = cartOutletItemList[cartItemIndex].crmUserTagIdList[j];

                                                                    if (upsellingItem.tagIdList.includes(productTagId)) {
                                                                        isValid = true;
                                                                        break;
                                                                    }
                                                                }
                                                            }

                                                            if (cartOutletItemList[cartItemIndex] && cartOutletItemList[cartItemIndex] && selectedOutletItemCategoriesDict && selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId] &&
                                                                selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList &&
                                                                selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList.length > 0) {
                                                                for (var j = 0; j < selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList.length; j++) {
                                                                    var categoryIdTagId = selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList[j];

                                                                    if (upsellingItem.tagIdList.includes(categoryIdTagId)) {
                                                                        isValid = true;
                                                                        break;
                                                                    }
                                                                }
                                                            }

                                                            if (isValid) {
                                                                break;
                                                            }
                                                        }

                                                        if (!isValid) {
                                                            // means no matched tag, try to find by category

                                                            // for (var cartItemIndex = 0; cartItemIndex < cartOutletItemList.length; cartItemIndex++) {
                                                            //     if (cartOutletItemList[cartItemIndex].categoryId === selectedOutletItemsRaw[i].categoryId) {
                                                            //         isValid = true;
                                                            //         break;
                                                            //     }
                                                            // }
                                                        }
                                                    }

                                                    if (isValid) {
                                                        if (
                                                            // toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)
                                                            upsellingCampaignsTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                                                        ) {
                                                            // means in the recommended list already, no need add
                                                        }
                                                        else {
                                                            // toRecommendedItemsTemp.push({
                                                            //     ...selectedOutletItemsRaw[i],
                                                            //     price: priceUpselling,

                                                            //     priceUpselling: priceUpselling,
                                                            //     upsellingCampaignId: upsellingCampaign.uniqueId,
                                                            // });

                                                            isValidCampaign = true;
                                                        }
                                                    }
                                                }
                                                else {
                                                    if (
                                                        // toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)
                                                        upsellingCampaignsTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                                                    ) {
                                                        // means in the recommended list already, no need add
                                                    }
                                                    else {
                                                        // toRecommendedItemsTemp.push({
                                                        //     ...selectedOutletItemsRaw[i],
                                                        //     price: priceUpselling,

                                                        //     priceUpselling: priceUpselling,
                                                        //     upsellingCampaignId: upsellingCampaign.uniqueId,
                                                        // });

                                                        isValidCampaign = true;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            if (isValidCampaign) {
                                break;
                            }
                        }

                        if (isValidCampaign) {
                            break;
                        }
                    }

                    // if (toRecommendedItemsTemp.length >= 3) {
                    //     break;
                    // }

                    if (isValidCampaign) {
                        upsellingCampaignsTemp.push(upsellingCampaign);
                    }
                }
            }

            global.upsellingCampaignsAfterCart = upsellingCampaignsTemp;

            // setToRecommendedItems(toRecommendedItemsTemp.slice(0, 3));
            DataStore.update(s => {
                s.upsellingCampaignsAfterCart = upsellingCampaignsTemp;
            });
        }
    }, [selectedOutletItemsRaw, selectedOutletItem, currCrmUser, selectedOutletCRMTagsDict, orderType, availableUpsellingCampaigns, selectedOutletItemCategoriesDict]);

    useEffect(() => {
        var variablePriceTemp = '0.00';

        if (selectedOutletItem &&
            // menuItem.uniqueId &&
            selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) {
            if (parseFloat(variablePrice) <= 0) {
                variablePriceTemp = selectedOutletItem.price.toFixed(2);
            }
        }

        if (onUpdatingCartItem &&
            onUpdatingCartItem.priceVariable !== undefined) {
            if (parseFloat(onUpdatingCartItem.priceVariable) >= 0) {
                variablePriceTemp = onUpdatingCartItem.priceVariable.toFixed(2);
            }
        }

        setVariablePrice(variablePriceTemp);
    }, [selectedOutletItem, onUpdatingCartItem]);

    useEffect(() => {
        setNewSelectedOutletItemAddOnDetails({});

        CommonStore.update(s => {
            s.selectedOutletItemAddOn = {};
            s.selectedOutletItemAddOnChoice = {};
            s.selectedOutletItemAddOnOi = {};
        });
    }, [
        selectedOutletItem,
        onUpdatingCartItem,
    ]);

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            if (selectedOutletItem && outletsItemAddOnDict[selectedOutletItem.uniqueId]) {
                var newSelectedOutletItemAddOnDetailsTemp = { ...newSelectedOutletItemAddOnDetails };

                var addOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId].filter(
                    (item) => item.minSelect === undefined && item.maxSelect === undefined
                );

                if (addOnList.length > 0) {
                    for (var i = 0; i < addOnList.length; i++) {
                        var addOn = addOnList[i];
                        if (addOn && outletsItemAddOnChoiceDict[addOn.uniqueId]) {
                            var addOnChoiceList = outletsItemAddOnChoiceDict[addOn.uniqueId];
                            for (var j = 0; j < addOnChoiceList.length; j++) {
                                var addOnChoice = addOnChoiceList[j];
                                if (!newSelectedOutletItemAddOnDetailsTemp[addOnChoice.uniqueId]) {
                                    newSelectedOutletItemAddOnDetailsTemp[addOnChoice.uniqueId] = {
                                        quantity: 0,
                                        price: addOnChoice.price,
                                        outletItemAddOnChoiceId: addOnChoice.uniqueId,
                                        outletItemAddOnId: addOn.uniqueId,
                                        choiceName: addOnChoice.name,
                                        addOnName: addOn.name,
                                        addOnId: addOn.uniqueId,
                                        minSelect: addOnChoice.minSelect,
                                        maxSelect: addOnChoice.maxSelect,
                                        oi: addOn.orderIndex !== undefined ? addOn.orderIndex : 0,
                                        pal: addOn.pal !== undefined ? addOn.pal : null,
                                        addOnMin: addOn.min ? addOn.min : 0,
                                        addOnMax: addOn.max ? addOn.max : 0,
                                        ...(addOn.skipSc) && { skipSc: addOn.skipSc },
                                        ...(addOn.hqr) && { hqr: addOn.hqr },
                                    };
                                }
                            }
                        }
                    }
                }

                // Auto Select Choices
                let selectedOutletItemAddOnTemp = { ...selectedOutletItemAddOn };
                let selectedOutletItemAddOnChoiceTemp = { ...selectedOutletItemAddOnChoice };

                const addons = outletsItemAddOnDict[selectedOutletItem.uniqueId];
                let hasAutoSelectItems = false;

                addons.forEach(addon => {
                    const isVariant = addon.minSelect !== undefined && addon.maxSelect !== undefined;

                    if (addon.isGroupAutoSelect && outletsItemAddOnChoiceDict[addon.uniqueId]) {
                        const choices = outletsItemAddOnChoiceDict[addon.uniqueId];

                        if (choices) {
                            const autoSelectChoices = choices.filter(choice => choice.isAutoSelect);

                            if (autoSelectChoices && autoSelectChoices.length > 0) {
                                hasAutoSelectItems = true;

                                // Variant Auto Select
                                if (isVariant) {
                                    if (!selectedOutletItemAddOnTemp[addon.uniqueId]) {
                                        selectedOutletItemAddOnTemp[addon.uniqueId] = {};
                                    }

                                    autoSelectChoices.forEach(choice => {
                                        selectedOutletItemAddOnTemp[addon.uniqueId] = {
                                            ...selectedOutletItemAddOnTemp[addon.uniqueId],
                                            [choice.uniqueId]: true
                                        };
                                        selectedOutletItemAddOnChoiceTemp[choice.uniqueId] = true;
                                    });
                                }
                                // Add On Auto Select
                                else {
                                    autoSelectChoices.forEach(choice => {
                                        newSelectedOutletItemAddOnDetailsTemp[choice.uniqueId] = {
                                            quantity: 1,
                                            price: choice.price,
                                            outletItemAddOnChoiceId: choice.uniqueId,
                                            outletItemAddOnId: addon.uniqueId,
                                            choiceName: choice.name,
                                            addOnName: addon.name,
                                            addOnId: addon.uniqueId,
                                            minSelect: choice.minSelect,
                                            maxSelect: choice.maxSelect,
                                            oi: addon.orderIndex !== undefined ? addon.orderIndex : 0,
                                            pal: addon.pal !== undefined ? addon.pal : null,
                                            addOnMin: addon.min ? addon.min : 0,
                                            addOnMax: addon.max ? addon.max : 0,
                                            ...(addon.skipSc) && { skipSc: addon.skipSc },
                                            ...(addon.hqr) && { hqr: addon.hqr },
                                        };
                                    });
                                }
                            }
                        }
                    }
                });

                CommonStore.update(s => {
                    if (hasAutoSelectItems) {
                        s.selectedOutletItemAddOn = selectedOutletItemAddOnTemp;
                        s.selectedOutletItemAddOnChoice = selectedOutletItemAddOnChoiceTemp;
                    }
                    setNewSelectedOutletItemAddOnDetails(newSelectedOutletItemAddOnDetailsTemp);
                });

                if (hasAutoSelectItems) {
                    setTimeout(() => {
                        let addOnPriceTemp = 0;

                        Object.entries(selectedOutletItemAddOnTemp).forEach(([addOnId, addOnChoices]) => {
                            Object.entries(addOnChoices).forEach(([choiceId, isSelected]) => {
                                if (isSelected) {
                                    const actualChoiceList = outletsItemAddOnChoiceDict[addOnId];
                                    if (actualChoiceList) {
                                        for (let i = 0; i < actualChoiceList.length; i++) {
                                            if (actualChoiceList[i].uniqueId === choiceId) {
                                                addOnPriceTemp += actualChoiceList[i].price;
                                            }
                                        }
                                    }
                                }
                            });
                        });

                        Object.values(newSelectedOutletItemAddOnDetailsTemp).forEach(addon => {
                            if (addon.quantity > 0 && !addon.hqr
                            ) {
                                addOnPriceTemp += addon.quantity * addon.price;
                            }
                        });

                        if (addOnPriceTemp > 0) {
                            setAddOnPrice(addOnPriceTemp);
                        }
                    }, 0);
                }
            }
        });
    }, [selectedOutletItem, outletsItemAddOnDict, outletsItemAddOnChoiceDict]);

    useEffect(() => {
        if (onUpdatingCartItem) {
            setModalQuantity(onUpdatingCartItem.quantity);

            return () => {
                CommonStore.update(s => {
                    s.onUpdatingCartItem = null;
                });
            }
        }
        else {
            setModalQuantity(1);
            setRemark('');
        }
    }, [onUpdatingCartItem, selectedOutletItem]);

    useEffect(() => {
        if (selectedOutletItem && selectedOutletItem.price !== undefined) {
            var extraPrice = 0;
            if (
                orderType === ORDER_TYPE.DELIVERY &&
                selectedOutlet &&
                selectedOutlet.deliveryPrice
            ) {
                extraPrice = selectedOutlet.deliveryPrice;
            } else if (
                orderType === ORDER_TYPE.PICKUP &&
                selectedOutlet &&
                selectedOutlet.pickUpPrice
            ) {
                extraPrice = selectedOutlet.pickUpPrice;
            }

            if (orderType === ORDER_TYPE.DELIVERY) {
                extraPrice = selectedOutletItem.deliveryCharges || 0;

                if (extraPrice && selectedOutletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                    extraPrice = selectedOutletItem.price * extraPrice / 100;
                }

                if (!selectedOutletItem.deliveryChargesActive) {
                    extraPrice = 0;
                }
            }

            if (orderType === ORDER_TYPE.PICKUP) {
                extraPrice = selectedOutletItem.pickUpCharges || 0;

                if (extraPrice && selectedOutletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                    extraPrice = selectedOutletItem.price * extraPrice / 100;
                }

                if (!selectedOutletItem.pickUpChargesActive) {
                    extraPrice = 0;
                }
            }

            var overrideCategoryPrice = undefined;
            if (selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] && !selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].excludePromoVoucher && overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name] !== undefined) {
                overrideCategoryPrice = overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name].overridePrice;
            }

            if (!selectedOutletItem.excludePromoVoucher && overrideItemPriceSkuDict[selectedOutletItem.sku] && overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice !== undefined) {
                setTotalPrice(overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice);
            }
            else if (overrideCategoryPrice !== undefined) {
                setTotalPrice(overrideCategoryPrice);
            }
            else {
                setTotalPrice(extraPrice + selectedOutletItem.price);
            }
        }
    }, [selectedOutletItem, overrideItemPriceSkuDict, overrideCategoryPriceNameDict]);

    const [addOnMinMaxMessage, setAddOnMinMaxMessage] = useState('');

    const [addOnMinMaxMessageResultList, setAddOnMinMaxMessageResultList] = useState([]);
    const [addOnMinMaxMessageResultDict, setAddOnMinMaxMessageResultDict] = useState({});

    const [sortedVariantAddOnList, setSortedVariantAddOnList] = useState([]);

    useEffect(() => {
        const addOnList = Object.entries(selectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));

        var addOnPriceTemp = 0;

        for (var i = 0; i < addOnList.length; i++) {
            var isValid = false;

            if (outletsItemAddOnDict[selectedOutletItem.uniqueId] && outletsItemAddOnDict[selectedOutletItem.uniqueId].length > 0) {
                for (var j = 0; j < outletsItemAddOnDict[selectedOutletItem.uniqueId].length; j++) {
                    if (outletsItemAddOnDict[selectedOutletItem.uniqueId][j].outletItemId === selectedOutletItem.uniqueId) {
                        isValid = true;
                    }
                }
            }

            if (isValid && addOnList[i].value) {
                const addOnChoiceList = Object.entries(addOnList[i].value).map(
                    ([key, value]) => ({ key: key, value: value }),
                );
                for (var j = 0; j < addOnChoiceList.length; j++) {

                    if (addOnChoiceList[j].value) {
                        const actualAddOnChoiceList = outletsItemAddOnChoiceDict[addOnList[i].key];

                        if (actualAddOnChoiceList && actualAddOnChoiceList.length > 0) {
                            for (var k = 0; k < actualAddOnChoiceList.length; k++) {
                                if (addOnChoiceList[j].key === actualAddOnChoiceList[k].uniqueId) {
                                    // add this addon price

                                    addOnPriceTemp += actualAddOnChoiceList[k].price;
                                }
                            }
                        }
                    }
                }
            }
        }
        //////////////////////////////////////////

        var addOnMinMaxMessageTemp = '';

        let addOnMinMaxMessageResultListTemp = [];

        let selectedAddOnIdForChoiceQtyDictTemp = {};

        // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));
        const newSelectedOutletItemAddOnList = Object.entries(
            newSelectedOutletItemAddOnDetails,
        ).map(([key, value]) => ({ key: key, value: value }));

        for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
            if (newSelectedOutletItemAddOnList[i].value) {
                const addOnTemp = newSelectedOutletItemAddOnDetails[newSelectedOutletItemAddOnList[i].key];

                if (addOnTemp && !addOnTemp.hqr) {
                    addOnPriceTemp += addOnTemp.quantity * addOnTemp.price;

                    if (selectedAddOnIdForChoiceQtyDictTemp[addOnTemp.outletItemAddOnId]) {
                        selectedAddOnIdForChoiceQtyDictTemp[addOnTemp.outletItemAddOnId].countedQty += addOnTemp.quantity;
                    }
                    else {
                        selectedAddOnIdForChoiceQtyDictTemp[addOnTemp.outletItemAddOnId] = {
                            ...addOnTemp,

                            countedQty: addOnTemp.quantity,
                        };
                    }

                    ///////////////////////////////

                    // checks addOn minSelect and maxSelect

                    if (addOnTemp.quantity < addOnTemp.minSelect || addOnTemp.quantity > addOnTemp.maxSelect) {
                        addOnMinMaxMessageTemp += `${addOnTemp.choiceName}'s quantity must be within ${addOnTemp.minSelect} ~ ${addOnTemp.maxSelect}\n`;

                        addOnMinMaxMessageResultListTemp.push(addOnTemp);
                    }

                    ///////////////////////////////
                }
            }
        }

        //////////////////////////////////////////

        const selectedAddOnIdForChoiceQtyDictTempList = Object.entries(selectedAddOnIdForChoiceQtyDictTemp).map(([key, value]) => {
            return {
                key: key,
                ...value,
            };
        });
        if (sortedVariantAddOnList.length > 0) {
            for (let vaIndex = 0; vaIndex < sortedVariantAddOnList.length; vaIndex++) {
                const vaGroup = sortedVariantAddOnList[vaIndex];

                if (vaGroup.min > 0 || vaGroup.max > 0) {
                    let vaChecked = false;

                    for (let i = 0; i < selectedAddOnIdForChoiceQtyDictTempList.length; i++) {
                        const addOnIdForChoice = selectedAddOnIdForChoiceQtyDictTempList[i];

                        if (addOnIdForChoice.addOnId === vaGroup.uniqueId) {
                            if (addOnIdForChoice.addOnMin > 0 || addOnIdForChoice.addOnMax > 0) {
                                if (addOnIdForChoice.countedQty < addOnIdForChoice.addOnMin || addOnIdForChoice.countedQty > addOnIdForChoice.addOnMax) {
                                    addOnMinMaxMessageTemp += `${addOnIdForChoice.addOnName}'s choices quantities must within ${addOnIdForChoice.addOnMin} ~ ${addOnIdForChoice.addOnMax}\n`;

                                    addOnMinMaxMessageResultListTemp.push(vaGroup);
                                }
                            }

                            vaChecked = true;
                            break;
                        }
                        else {
                            // if (vaGroup.min > 0) {
                            //     addOnMinMaxMessageTemp += `${vaGroup.name}'s choices quantities must within ${vaGroup.min} ~ ${vaGroup.max}\n`;

                            //     vaChecked = true;
                            //     break;
                            // }
                        }
                    }

                    if (!vaChecked) {
                        if (vaGroup.min > 0) {
                            addOnMinMaxMessageTemp += `${vaGroup.name}'s choices quantities must within ${vaGroup.min} ~ ${vaGroup.max}\n`;

                            addOnMinMaxMessageResultListTemp.push(vaGroup);

                            vaChecked = true;
                            break;
                        }
                    }
                }
            }
        }

        //////////////////////////////////////////

        setAddOnMinMaxMessage(addOnMinMaxMessageTemp);

        setAddOnMinMaxMessageResultList(addOnMinMaxMessageResultListTemp);

        //////////////////////////////////////////

        setAddOnPrice(addOnPriceTemp);

        CommonStore.update(s => {
            s.selectedAddOnIdForChoiceQtyDict = selectedAddOnIdForChoiceQtyDictTemp;
        });
    }, [
        selectedOutletItemAddOn,
        selectedOutletItem,
        outletsItemAddOnChoiceDict,
        outletsItemAddOnDict,

        newSelectedOutletItemAddOn,
        newSelectedOutletItemAddOnDetails,

        sortedVariantAddOnList,
    ]);

    useEffect(() => {
        if (!isLoading) {
            if (selectedOutletItem && selectedOutletItem.uniqueId) {
                let addOnVerifiedResultListTemp = [];

                const addOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId]
                    ? outletsItemAddOnDict[selectedOutletItem.uniqueId].filter(
                        (item) => {
                            // !(item.isHideAddOn === true || item.isHideVariant === true)

                            const hidden = item.isHideAddOn === true || item.isHideVariant === true
                            // || item.hqr === true;
                            if (hidden) return false;

                            const ots = item.ots || [];
                            return ots.length === 0 || ots.includes('DEFAULT') || (orderType === ORDER_TYPE_SUB.OTHER_DELIVERY && ots.includes(ORDER_TYPE.DELIVERY)) || (orderType === ORDER_TYPE_SUB.NORMAL && ots.includes(orderType)); // <-- the main filter
                        }
                    )
                    : [];

                if (addOnList && addOnList.length > 0) {
                    // got addons

                    var resultList = [];

                    for (var i = 0; i < addOnList.length; i++) {
                        if (addOnList[i].minSelect !== undefined && addOnList[i].maxSelect !== undefined) {
                            var result = false;

                            const addOnId = addOnList[i].uniqueId;
                            const minSelect = addOnList[i].minSelect;
                            const maxSelect = addOnList[i].maxSelect;

                            if (minSelect === 0) {
                                result = true;
                            } else if (selectedOutletItemAddOn[addOnId]) {
                                const selectedOutletItemAddOnValueList = Object.entries(selectedOutletItemAddOn[addOnId]).map(([key, value]) => (value));

                                const selectedCount = selectedOutletItemAddOnValueList.filter(value => value === true).length;

                                if (selectedCount >= minSelect && selectedCount <= maxSelect) {
                                    result = true;
                                }
                            }

                            resultList.push(result);

                            if (!result) {
                                addOnVerifiedResultListTemp.push(addOnList[i]);
                            }
                        }
                        else {
                            resultList.push(true);
                        }
                    }

                    setAddOnVerified(resultList.filter(result => result === false).length === 0);
                    setAddOnVerifiedResultList(addOnVerifiedResultListTemp);
                }
                else {
                    setAddOnVerified(true);
                    setAddOnVerifiedResultList([]);
                }
            }
        }
    }, [isLoading, selectedOutletItem, outletsItemAddOnDict, outletsItemAddOnChoiceDict, selectedOutletItemAddOn]);

    useEffect(() => {
        let sortedVariantAddOnListTemp = [];

        if (selectedOutletItem && selectedOutletItem.uniqueId && outletsItemAddOnDict[selectedOutletItem.uniqueId]) {
            sortedVariantAddOnListTemp = outletsItemAddOnDict[selectedOutletItem.uniqueId].filter((item) => {
                const hidden = item.isHideAddOn === true || item.isHideVariant === true || item.hqr === true;
                if (hidden) return false;

                const ots = item.ots || [];
                return ots.length === 0 || ots.includes('DEFAULT') || ots.includes(orderType); // <-- the main filter
            });

            sortedVariantAddOnListTemp = sortedVariantAddOnListTemp.sort((a, b) => {
                return (
                    ((a.orderIndex !== undefined)
                        ? a.orderIndex
                        : sortedVariantAddOnListTemp.length) -
                    ((b.orderIndex !== undefined)
                        ? b.orderIndex
                        : sortedVariantAddOnListTemp.length)
                );
            });
        }

        setSortedVariantAddOnList(sortedVariantAddOnListTemp);
    }, [selectedOutletItem, outletsItemAddOnDict, orderType]);

    useEffect(() => {
        if (global.outletName) {
            logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS, {
                eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS,

                outletName: global.outletName ? `${global.outletName} (Web)` : '',

                webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
            });
        }
    }, []);

    const updateUserCart = async (newCartItems) => {
        // const body = {
        //     userId: firebaseUid,
        //     outletId: selectedOutlet.uniqueId,
        //     tableId: selectedOutletTableId,
        //     tablePax: selectedOutletTablePax,
        //     cartItems: newCartItems,

        //     waiterId: selectedOutletWaiterId,
        // };

        // ApiClient.POST(API.updateUserCart, body).then((result) => {
        //     if (result && result.status === 'success') {
        //         console.log('ok');
        //     }
        // });
    };
    const renderVariants = (dataItem, item, index) => {
        const item2 = dataItem.item;

        return (
            <>
                {//////////////////////////////////////////////////////////////////

                    // // 2022-07-19 - Added variant Image for item - Eric Cheng Num Keet
                }
                <View>
                    {
                        (selectedOutlet && selectedOutlet.merchantType === APP_TYPE.RETAIL)
                            ?
                            <>
                                {item2.image ? (
                                    <AsyncImage
                                        source={{ uri: item2.image }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: 50,
                                            height: 50,
                                            borderRadius: 10,
                                            marginBottom: 15,
                                            alignSelf: 'center',
                                        }}
                                        hideLoading={true}
                                    />) : (
                                    <View
                                        style={{
                                            width: 50,
                                            height: 50,
                                            backgroundColor: Colors.secondaryColor,
                                            borderRadius: 10,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginBottom: 15,
                                            alignSelf: 'center',
                                        }}>
                                        <DefaultImage />
                                    </View>
                                )
                                    //////////////////////////////////////////////////////////////////////
                                }
                            </>
                            :
                            <></>
                    }
                    <TouchableOpacity style={{
                        //width: "50%",
                        height: "auto",
                        // backgroundColor: selectedOutletItemAddOnChoice[item2.uniqueId] ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
                        // borderColor: selectedOutletItemAddOnChoice[item2.uniqueId] ? 'transparent' : Colors.descriptionColor,
                        // borderColor: Colors.descriptionColor,
                        // marginHorizontal: 8,
                        // paddingHorizontal: 12,
                        marginVertical: 10,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                    }}
                        onPress={() => {
                            if (global.outletName) {
                                logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_VARIANT_CLICK, {
                                    eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_VARIANT_CLICK,

                                    outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                    webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                });
                            }

                            /////////////////////////////////
                            // minSelect/maxSelect calculations

                            var selectedOutletItemAddOnChoiceRemoved = {};

                            if (item.minSelect === 1 && item.maxSelect === 1) {
                                // means can only choose 1 addon choice, the others need removed

                                for (var i = 0; i < outletsItemAddOnChoiceDict[item.uniqueId].length; i++) {
                                    selectedOutletItemAddOnChoiceRemoved[outletsItemAddOnChoiceDict[item.uniqueId][i].uniqueId] = false;
                                }
                            }

                            /////////////////////////////////

                            var chooseValue = false;
                            if (selectedOutletItemAddOnChoice[item2.uniqueId] === undefined ||
                                selectedOutletItemAddOnChoice[item2.uniqueId] === false) {
                                chooseValue = true;
                            }
                            else {
                                chooseValue = false;
                            }

                            //////////////////////////////////////////////////////////////////

                            // 2023-01-09 - Prevent user from select the choice num exceed the max choice num

                            var maxChoices = item.maxSelect;
                            var totalChoiceNum = 0;
                            var choseChoiceIdList = [];

                            if (chooseValue && selectedOutletItemAddOn[item.uniqueId]) {
                                for (
                                    var i = 0;
                                    i <
                                    outletsItemAddOnChoiceDict[
                                        item.uniqueId
                                    ].length;
                                    i++
                                ) {
                                    var currChoiceId = outletsItemAddOnChoiceDict[
                                        item.uniqueId
                                    ][i].uniqueId;

                                    if (selectedOutletItemAddOnChoice[currChoiceId]) {
                                        // means selected

                                        totalChoiceNum++;

                                        choseChoiceIdList.push(currChoiceId);
                                    }
                                }
                            }

                            if (chooseValue && totalChoiceNum >= maxChoices && choseChoiceIdList.length > 0) {
                                // try to remove one of the previous choice

                                selectedOutletItemAddOnChoiceRemoved[
                                    choseChoiceIdList[0]
                                ] = false;

                                // chooseValue = false;
                            }

                            //////////////////////////////////////////////////////////////////

                            CommonStore.update(s => {
                                s.selectedOutletItemAddOnChoice = {
                                    ...selectedOutletItemAddOnChoice,

                                    ...selectedOutletItemAddOnChoiceRemoved,

                                    [item2.uniqueId]: chooseValue,
                                };

                                if (selectedOutletItemAddOn[item.uniqueId] === undefined) {
                                    s.selectedOutletItemAddOn = {
                                        ...selectedOutletItemAddOn,
                                        // [item.uniqueId]: new Set(),
                                        [item.uniqueId]: {},
                                    };
                                }

                                s.selectedOutletItemAddOn = {
                                    ...selectedOutletItemAddOn,
                                    [item.uniqueId]: {
                                        ...(selectedOutletItemAddOn[item.uniqueId]),

                                        ...selectedOutletItemAddOnChoiceRemoved,

                                        [item2.uniqueId]: chooseValue,
                                    },
                                };

                                s.selectedOutletItemAddOnOi = {
                                    ...selectedOutletItemAddOnOi,
                                    [item.uniqueId]: item.orderIndex !== undefined ? item.orderIndex : 0,
                                };

                                console.log({
                                    ...selectedOutletItemAddOn,
                                    [item.uniqueId]: {
                                        ...(selectedOutletItemAddOn[item.uniqueId]),

                                        ...selectedOutletItemAddOnChoiceRemoved,

                                        [item2.uniqueId]: chooseValue,
                                    },
                                });
                            });
                        }}>
                        <View style={{ flexDirection: 'row', flex: 1, flexWrap: 'wrap' }}>
                            <Text
                                testID={`${index}-variantName-${dataItem.index}`}
                                style={{
                                    alignSelf: "center",
                                    color: Colors.blackColor,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: 14,
                                    maxWidth: '90%',
                                }}>{`${item2.name}`}
                            </Text>
                        </View>

                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Text style={{
                                alignSelf: "center",
                                color: Colors.blackColor,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                                paddingRight: 5,
                            }}>{`+ RM ${item2.price.toFixed(2)}`}
                            </Text>

                            {/* <CheckboxLoadable fallback={<></>}>
                                {({ default: Checkbox }) =>
                                    <Checkbox checked={selectedOutletItemAddOnChoice[item2.uniqueId] ? true : false} />
                                }
                            </CheckboxLoadable> */}

                            <Checkbox checked={selectedOutletItemAddOnChoice[item2.uniqueId] ? true : false} />
                        </View>

                    </TouchableOpacity>
                </View>
            </>
        );
    };

    const renderAddons = (dataItem, item) => {
        const item2 = dataItem.item;

        return (
            <View style={{
                width: 'auto%',
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: 10,
                marginBottom: 10,
                justifyContent: 'space-between',
            }}>
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    width: '45%',
                    // backgroundColor: 'red',
                }}>
                    {//////////////////////////////////////////////////////////////////

                        // // 2022-07-19 - Added addon Image for item - Eric Cheng Num Keet
                    }
                    {
                        (selectedOutlet && selectedOutlet.merchantType === APP_TYPE.RETAIL)
                            ?
                            <>
                                {item2.image ? (
                                    <AsyncImage
                                        source={{ uri: item2.image }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: 50,
                                            height: 50,
                                            borderRadius: 10,
                                            marginRight: 20,
                                        }}
                                        hideLoading={true}
                                    />) : (
                                    <View
                                        style={{
                                            width: 50,
                                            height: 50,
                                            backgroundColor: Colors.secondaryColor,
                                            borderRadius: 10,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginRight: 20,
                                        }}>
                                        <DefaultImage />
                                    </View>
                                )
                                    //////////////////////////////////////////////////////////////////////
                                }
                            </>
                            :
                            <></>
                    }

                    <Text style={{
                        fontFamily: isMaxAddonsTest && item2.name === "Dessert 2" ? 'NunitoSans-Regular' : 'NunitoSans-Bold',
                        fontSize: 14,
                        color: isMaxAddonsTest && item2.name === "Dessert 2" ? Colors.tabGrey : Colors.blackColor
                        // marginLeft: 15,
                    }}>
                        {item2.name}
                    </Text>
                </View>

                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    // backgroundColor: 'red',                                
                    justifyContent: 'space-between',
                    // width: windowWidth * 0.4,
                    width: '55%',
                }}>
                    <View
                        style={{
                            flexDirection: "row",
                            borderWidth: 1,
                            borderRadius: 25,
                            borderColor: isMaxAddonsTest ? Colors.tabGrey : Colors.primaryColor,
                            alignItems: 'center',
                            width: '35%',
                            justifyContent: 'space-around'
                        }}>
                        <TouchableOpacity
                            onPress={() => {
                                if (global.outletName) {
                                    logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_ADDON_CLICK, {
                                        eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_ADDON_CLICK,

                                        outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                        webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                    });
                                }

                                var quantityTemp = 0;

                                if (newSelectedOutletItemAddOnDetails[item2.uniqueId]) {
                                    quantityTemp = newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity;

                                    quantityTemp = quantityTemp - 1 >= 0 ? quantityTemp - 1 : 0;

                                    setNewSelectedOutletItemAddOnDetails({
                                        ...newSelectedOutletItemAddOnDetails,
                                        [item2.uniqueId]: {
                                            ...newSelectedOutletItemAddOnDetails[item2.uniqueId],
                                            quantity: quantityTemp,
                                            price: item2.price,

                                            outletItemAddOnChoiceId: item2.uniqueId,
                                            outletItemAddOnId: item.uniqueId,

                                            choiceName: item2.name,
                                            addOnName: item.name,

                                            addOnId: item.uniqueId,

                                            minSelect: item2.minSelect,
                                            maxSelect: item2.maxSelect,

                                            oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                                            pal: item.pal !== undefined ? item.pal : null,

                                            addOnMin: item.min ? item.min : 0,
                                            addOnMax: item.max ? item.max : 0,

                                            ...(item.skipSc) && {
                                                skipSc: item.skipSc,
                                            },

                                            ...(item.hqr) && {
                                                hqr: item.hqr,
                                            },

                                            ...(item.ost) && {
                                                ost: item.ost,
                                            },
                                        }
                                    });
                                }
                                else {
                                    setNewSelectedOutletItemAddOnDetails({
                                        ...newSelectedOutletItemAddOnDetails,
                                        [item2.uniqueId]: {
                                            quantity: quantityTemp,
                                            price: item2.price,

                                            outletItemAddOnChoiceId: item2.uniqueId,
                                            outletItemAddOnId: item.uniqueId,

                                            choiceName: item2.name,
                                            addOnName: item.name,

                                            addOnId: item.uniqueId,

                                            minSelect: item2.minSelect,
                                            maxSelect: item2.maxSelect,

                                            oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                                            pal: item.pal !== undefined ? item.pal : null,

                                            addOnMin: item.min ? item.min : 0,
                                            addOnMax: item.max ? item.max : 0,

                                            ...(item.skipSc) && {
                                                skipSc: item.skipSc,
                                            },

                                            ...(item.hqr) && {
                                                hqr: item.hqr,
                                            },

                                            ...(item.ost) && {
                                                ost: item.ost,
                                            },
                                        }
                                    });
                                }
                            }}>
                            <View testID={`addOnMinus-${dataItem.index}`} style={[styles.addBtn, { height: 27, }]}>
                                <FontAwesome name="minus"
                                    color={isMaxAddonsTest ? Colors.tabGrey : Colors.primaryColor}
                                    size={10}
                                />
                            </View>
                        </TouchableOpacity>
                        <View style={[styles.addBtn, { height: 27, }]} >
                            <Text
                                testID={`addOnQuantity-${dataItem.index}`}
                                style={{
                                    fontSize: 14,
                                    fontFamily: "NunitoSans-Bold",
                                    color: isMaxAddonsTest ? Colors.tabGrey : Colors.primaryColor,
                                }}>
                                {newSelectedOutletItemAddOnDetails[item2.uniqueId] ?
                                    newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity
                                    : 0
                                }
                            </Text>
                        </View>

                        <TouchableOpacity
                            onPress={() => {
                                if (global.outletName) {
                                    logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_ADDON_CLICK, {
                                        eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_ADDON_CLICK,

                                        outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                        webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                    });
                                }

                                let allowToProceed = false;
                                if (item.min > 0 || item.max > 0) {
                                    const addOnQtyTotal = (selectedAddOnIdForChoiceQtyDict[item.uniqueId] && selectedAddOnIdForChoiceQtyDict[item.uniqueId].countedQty) ? selectedAddOnIdForChoiceQtyDict[item.uniqueId].countedQty : 0;

                                    if ((addOnQtyTotal + 1) > item.max) {

                                    }
                                    else {
                                        allowToProceed = true;
                                    }
                                }
                                else {
                                    allowToProceed = true;
                                }

                                if (!allowToProceed) {
                                    // window.confirm(`Info\n\nThe maximum selection for this add-on is ${item.max}`);

                                    Toastify({
                                        text: `The maximum selection for this add-on is ${item.max}`,
                                        duration: 3000,
                                        // destination: "https://github.com/apvarun/toastify-js",
                                        newWindow: true,
                                        close: false,
                                        gravity: "top", // `top` or `bottom`
                                        position: "right", // `left`, `center` or `right`
                                        stopOnFocus: true, // Prevents dismissing of toast on hover
                                        style: {
                                            background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
                                            color: 'white',

                                            // marginLeft: '15px !important',
                                            // marginRight: '15px !important',
                                        },
                                        onClick: function () { } // Callback after click
                                    }).showToast();

                                    return;
                                }

                                var quantityTemp = 0;

                                if (newSelectedOutletItemAddOnDetails[item2.uniqueId]) {
                                    quantityTemp = newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity;

                                    quantityTemp = quantityTemp + 1;

                                    setNewSelectedOutletItemAddOnDetails({
                                        ...newSelectedOutletItemAddOnDetails,
                                        [item2.uniqueId]: {
                                            ...newSelectedOutletItemAddOnDetails[item2.uniqueId],
                                            quantity: quantityTemp,
                                            price: item2.price,

                                            outletItemAddOnChoiceId: item2.uniqueId,
                                            outletItemAddOnId: item.uniqueId,

                                            choiceName: item2.name,
                                            addOnName: item.name,

                                            addOnId: item.uniqueId,

                                            minSelect: item2.minSelect,
                                            maxSelect: item2.maxSelect,

                                            oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                                            pal: item.pal !== undefined ? item.pal : null,

                                            addOnMin: item.min ? item.min : 0,
                                            addOnMax: item.max ? item.max : 0,

                                            ...(item.skipSc) && {
                                                skipSc: item.skipSc,
                                            },

                                            ...(item.hqr) && {
                                                hqr: item.hqr,
                                            },

                                            ...(item.ost) && {
                                                ost: item.ost,
                                            },

                                        }
                                    });
                                }
                                else {
                                    setNewSelectedOutletItemAddOnDetails({
                                        ...newSelectedOutletItemAddOnDetails,
                                        [item2.uniqueId]: {
                                            quantity: 1,
                                            price: item2.price,

                                            outletItemAddOnChoiceId: item2.uniqueId,
                                            outletItemAddOnId: item.uniqueId,

                                            choiceName: item2.name,
                                            addOnName: item.name,

                                            addOnId: item.uniqueId,

                                            minSelect: item2.minSelect,
                                            maxSelect: item2.maxSelect,

                                            oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                                            pal: item.pal !== undefined ? item.pal : null,

                                            addOnMin: item.min ? item.min : 0,
                                            addOnMax: item.max ? item.max : 0,

                                            ...(item.skipSc) && {
                                                skipSc: item.skipSc,
                                            },

                                            ...(item.hqr) && {
                                                hqr: item.hqr,
                                            },

                                            ...(item.ost) && {
                                                ost: item.ost,
                                            },

                                        }
                                    });
                                }
                            }}>
                            <View testID={`addOnPlus-${dataItem.index}`} style={[styles.addBtn, { height: 27, }]}>
                                <FontAwesome name="plus"
                                    color={isMaxAddonsTest ? Colors.tabGrey : Colors.primaryColor}
                                    size={10}
                                />
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={{
                        width: '65%',
                        flexDirection: 'row',
                        // backgroundColor: 'blue',
                        justifyContent: 'flex-end',
                    }}>
                        <Text style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                            marginLeft: 10,
                        }}>+ RM {((newSelectedOutletItemAddOnDetails[item2.uniqueId] ?
                            (newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity > 0 ? newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity : 1)
                            : 1) * item2.price).toFixed(2)
                            }</Text>
                    </View>
                </View>
            </View>
        );
    };
    /////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////

    // Check if item or category excludes promo/voucher
    let excludePromoVoucher = false;
    let outletCategory = selectedOutletItemCategoriesDict[selectedOutletItem.categoryId];
    if (
        (selectedOutletItem && selectedOutletItem.excludePromoVoucher)
        ||
        (outletCategory && outletCategory.excludePromoVoucher)
    ) {
        excludePromoVoucher = true;
    }

    var overrideCategoryPrice = undefined;
    if (!excludePromoVoucher && selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] && overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name] !== undefined) {
        overrideCategoryPrice = overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name].overridePrice;
    }
    var extraPrice = 0;
    if (
        orderType === ORDER_TYPE.DELIVERY &&
        selectedOutlet &&
        selectedOutlet.deliveryPrice
    ) {
        extraPrice = selectedOutlet.deliveryPrice;
    } else if (
        orderType === ORDER_TYPE.PICKUP &&
        selectedOutlet &&
        selectedOutlet.pickUpPrice
    ) {
        extraPrice = selectedOutlet.pickUpPrice;
    }

    if (orderType === ORDER_TYPE.DELIVERY) {
        extraPrice = selectedOutletItem.deliveryCharges || 0;

        if (extraPrice && selectedOutletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
            extraPrice = selectedOutletItem.price * extraPrice / 100;
        }

        if (!selectedOutletItem.deliveryChargesActive) {
            extraPrice = 0;
        }
    }

    if (orderType === ORDER_TYPE.PICKUP) {
        extraPrice = selectedOutletItem.pickUpCharges || 0;

        if (extraPrice && selectedOutletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
            extraPrice = selectedOutletItem.price * extraPrice / 100;
        }

        if (!selectedOutletItem.pickUpChargesActive) {
            extraPrice = 0;
        }
    }

    /////////////////////////////////////////////////////////////////////////

    return (
        <SafeAreaProvider
        // edges={['right', 'bottom', 'left']}
        >
            <Modal
                style={{
                    // flex: 1,
                    width: windowWidth,
                    height: windowHeight,
                }}
                visible={menuItemDetailModal}
                transparent={false}
                animationType="none"
            >
                <View
                    style={{
                        backgroundColor: Colors.whiteColor,
                        // flex: 1,
                        // minHeight: windowHeight,

                        width: windowWidth,
                        height: windowHeight,

                        position: 'relative',
                    }}
                >
                    <TouchableOpacity
                        onPress={() => {
                            CommonStore.update((s) => {
                                s.menuItemDetailModal = false;
                            });
                            setModalQuantity(1);
                            setNewSelectedOutletItemAddOnDetails({});

                            global.menuItemDetailModal = false;

                            if (global.outletName) {
                                logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_BACK_CLICK, {
                                    eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_BACK_CLICK,

                                    outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                    webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                });
                            }

                            if (global.isFromRecommendedItems) {
                                if (global.currUpsellingSection === UPSELLING_SECTION.AFTER_CART) {
                                    CommonStore.update(s => {
                                        s.currPageIframe = 'UpsellingAfterCart';
                                    });
                                }
                                else if (global.currUpsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT ||
                                    global.currUpsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT_RECOMMENDATION) {
                                    CommonStore.update(s => {
                                        s.currPageIframe = 'UpsellingAfterCheckout';
                                    });
                                }
                                else if (global.currUpsellingSection === UPSELLING_SECTION.IN_CART) {
                                    CommonStore.update(s => {
                                        s.currPageIframe = 'Cart';
                                    });
                                }
                            }
                            else {
                                CommonStore.update(s => {
                                    s.currPageIframe = 'OutletMenu';
                                });
                            }

                            global.isFromRecommendedItems = false;
                        }}
                    >
                        <View
                            style={{
                                position: "absolute",
                                left: 15,
                                top: 17,
                                index: 9000,
                            }}
                        >
                            <Close name="closecircle" size={selectedOutletItem && selectedOutletItem.image ? 30 : 25} /* color={"#b0b0b0"} */ color={Colors.blackColor} />
                        </View>
                    </TouchableOpacity>

                    <View style={[{
                        backgroundColor: Colors.whiteColor,
                        width: selectedOutletItem && selectedOutletItem.image ? '100%' : 0,
                        height: selectedOutletItem && selectedOutletItem.image ? Dimensions.get('window').height * 0.43 : 0,
                        borderRadius: 10, zIndex: -1,
                    }, selectedOutletItem && selectedOutletItem.image ? {

                    } : {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }]}>
                        {selectedOutletItem && selectedOutletItem.image ?
                            <AsyncImage source={{ uri: selectedOutletItem.image }} item={selectedOutletItem} style={{
                                width: '100%',
                                height: Dimensions.get('window').height * 0.43,
                            }} />
                            :
                            <></>
                        }
                    </View>

                    <ScrollView
                        ref={addOnScrollViewRef}
                        style={{
                            position: 'absolute', bottom: 0,
                            // left: selectedOutletItem && selectedOutletItem.image ? 25.5 : 0,
                            // width: selectedOutletItem && selectedOutletItem.image ? windowWidth * 0.865 : windowWidth,
                            width: windowWidth,
                            paddingBottom: 80, paddingHorizontal: 20,
                            backgroundColor: Colors.whiteColor,
                            borderRadius: 25,
                            height: selectedOutletItem && selectedOutletItem.image ? windowHeight * 0.65 : windowHeight * 0.93, borderWidth: 0.1, borderColor: Colors.fieldtBgColor,
                            zIndex: -1,
                        }}>
                        <View style={{
                        }}>
                            <View>
                                <View style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    // flexShrink: 1,
                                    width: '100%',
                                    paddingTop: selectedOutletItem && selectedOutletItem.image ? 20 : 20,
                                }}>
                                    <View style={{ width: '70%' }}>
                                        <Text
                                            // numberOfLines={2}
                                            style={{
                                                fontSize: 20,
                                                textTransform: 'uppercase',
                                                fontFamily: "NunitoSans-Bold",
                                            }}>{selectedOutletItem && selectedOutletItem.dpName ? selectedOutletItem.dpName : (selectedOutletItem.name ? selectedOutletItem.name : '')}
                                        </Text>
                                    </View>
                                    <View style={{ width: '30%', justifyContent: 'center', }}>
                                        <Text style={{
                                            color: Colors.primaryColor,
                                            fontFamily: "NunitoSans-Bold",
                                            textAlign: 'right',
                                            // paddingTop: 5,
                                            fontSize: 16,
                                        }}>RM{
                                                selectedOutletItem && selectedOutletItem.sku && !excludePromoVoucher && (overrideItemPriceSkuDict[selectedOutletItem.sku] !== undefined ||
                                                    overrideCategoryPrice !== undefined)
                                                    ?
                                                    (
                                                        overrideItemPriceSkuDict[selectedOutletItem.sku] && overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice !== undefined
                                                            ?
                                                            parseFloat(
                                                                overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice
                                                            ).toFixed(2)
                                                            :
                                                            parseFloat(
                                                                overrideCategoryPrice,
                                                            ).toFixed(2)
                                                    ) :
                                                    parseFloat(extraPrice + selectedOutletItem.price).toFixed(2)
                                            }{selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[selectedOutletItem.unitType]}` : ''}
                                        </Text>
                                    </View>
                                </View>
                                <Text
                                    // numberOfLines={1} 
                                    style={{
                                        fontSize: 14,
                                        fontFamily: "NunitoSans-Regular",
                                        paddingTop: 5,
                                        color: '#86898f',
                                    }}>{selectedOutletItem && selectedOutletItem.description ? `(${selectedOutletItem.description})` : ''}
                                </Text>
                                <View style={{ borderBottomWidth: 2, borderStyle: 'dotted', marginVertical: 25, opacity: 0.6, borderColor: 'grey' }} />
                            </View>

                            {isLoading ?
                                <View>
                                    <ActivityIndicator color={Colors.primaryColor} size={"large"} />
                                </View>
                                :
                                <>
                                    {
                                        (outletsItemAddOnDict[selectedOutletItem.uniqueId]
                                            ? outletsItemAddOnDict[selectedOutletItem.uniqueId] : []).length > 0
                                            ?
                                            <Text
                                                style={{
                                                    color: Colors.blackColor,
                                                    marginBottom: 10,
                                                    fontSize: 18,
                                                    fontFamily: 'NunitoSans-SemiBold',
                                                }}
                                            >
                                                Let's make it better?
                                            </Text>
                                            :
                                            <></>
                                    }
                                    {sortedVariantAddOnList
                                        ? sortedVariantAddOnList.map((item, index) => {

                                            if (item.minSelect !== undefined && item.maxSelect !== undefined) {
                                                // means is variant

                                                return (
                                                    <>
                                                        <View style={{
                                                            marginBottom: 20,
                                                        }}
                                                            onLayout={(event) => {
                                                                const { width, height, x, y, } = event.nativeEvent.layout;

                                                                if (item && item.uniqueId) {
                                                                    global.addOnPosYByIdDict[item.uniqueId] = y;
                                                                }
                                                            }}
                                                        >
                                                            <View style={{ flexDirection: "row", justifyContent: 'space-between' }}>
                                                                <Text
                                                                    style={{
                                                                        color: addOnVerifiedResultDict[item.uniqueId] ? Colors.tabRed : Colors.primaryColor,
                                                                        fontSize: 16,
                                                                        fontFamily: 'NunitoSans-SemiBold',
                                                                    }}
                                                                >
                                                                    {item.name ? item.name : 'N/A'}
                                                                </Text>
                                                                <Text
                                                                    style={{
                                                                        color: '#282C3F',
                                                                        fontSize: 12,
                                                                        fontFamily: 'NunitoSans-SemiBold',
                                                                        backgroundColor: '#56FD481A',
                                                                        paddingVertical: 2,
                                                                        paddingHorizontal: 10,
                                                                        borderColor: Colors.primaryColor,
                                                                        borderWidth: 1,
                                                                        borderRadius: 5,
                                                                        opacity: 0.7,
                                                                    }}
                                                                >
                                                                    {item.minSelect === 0 ? 'Optional' : 'Compulsory'}
                                                                </Text>
                                                            </View>
                                                            <Text
                                                                style={{
                                                                    color: '#86898F',
                                                                    fontSize: 12,
                                                                    fontFamily: 'NunitoSans-SemiBold',
                                                                }}
                                                            >
                                                                {item.maxSelect > 1 ? `(Please select up to ${item.maxSelect} choices)` : ''}
                                                            </Text>

                                                            <FlatList
                                                                horizontal={false}
                                                                nestedScrollEnabled={true}
                                                                data={outletsItemAddOnChoiceDict[item.uniqueId] ? outletsItemAddOnChoiceDict[item.uniqueId].filter(choiceFilter => {
                                                                    return !choiceFilter.isHidden;
                                                                }) : []}
                                                                renderItem={dataItem => renderVariants(dataItem, item)}
                                                                showsHorizontalScrollIndicator={false}
                                                                style={{
                                                                    marginTop: 10,

                                                                }}
                                                            />
                                                        </View>
                                                        <View style={{ borderBottomWidth: 2, borderStyle: 'dotted', marginBottom: 25, opacity: 0.6, borderColor: 'grey' }} />
                                                    </>
                                                );
                                            }
                                            else {
                                                // means is addon

                                                return (
                                                    <View style={{ marginBottom: 15 }}
                                                        onLayout={(event) => {
                                                            const { width, height, x, y, } = event.nativeEvent.layout;

                                                            if (item && item.uniqueId) {
                                                                global.addOnPosYByIdDict[item.uniqueId] = y;
                                                            }
                                                        }}
                                                    >
                                                        <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', }}>
                                                            <Text
                                                                style={{
                                                                    color: addOnMinMaxMessageResultDict[item.uniqueId] ? Colors.tabRed : Colors.primaryColor,
                                                                    fontSize: 16,
                                                                    fontFamily: 'NunitoSans-SemiBold',
                                                                }}
                                                            >
                                                                {item.name ? item.name : 'N/A'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    color: '#a8a8a8',
                                                                    backgroundColor: '#eaeaea',
                                                                    textAlign: 'center',
                                                                    justifyContent: 'center',
                                                                    // fontWeight: "500",
                                                                    // fontSize: 16,
                                                                    marginBottom: 0,
                                                                    // color: Colors.mainTxtColor,
                                                                    // fontWeight: "500",
                                                                    fontSize: 10,
                                                                    fontFamily: 'NunitoSans-SemiBold',
                                                                    marginLeft: 8,
                                                                    borderRadius: 10,
                                                                    paddingHorizontal: 7,
                                                                    paddingVertical: 2,
                                                                }}
                                                            >
                                                                {`${(selectedAddOnIdForChoiceQtyDict[item.uniqueId] && selectedAddOnIdForChoiceQtyDict[item.uniqueId].countedQty) ? selectedAddOnIdForChoiceQtyDict[item.uniqueId].countedQty : 0} Selected`}
                                                            </Text>
                                                        </View>

                                                        {
                                                            (item.min > 0 || item.max > 0)
                                                                ?
                                                                <Text
                                                                    style={{
                                                                        color: 'red',
                                                                        backgroundColor: '#fffbe8',
                                                                        // backgroundColor: Colors.secondaryColor,
                                                                        textAlign: 'center',
                                                                        justifyContent: 'center',
                                                                        // fontWeight: "500",
                                                                        // fontSize: 16,
                                                                        marginBottom: 0,
                                                                        // color: Colors.mainTxtColor,
                                                                        // fontWeight: "500",
                                                                        fontSize: 13,
                                                                        fontFamily: 'NunitoSans-SemiBold',
                                                                        fontWeight: 'bold',
                                                                        marginTop: 20,
                                                                        padding: 6,
                                                                    }}
                                                                >
                                                                    {`Required - Select ${item.min} - ${item.max} only`}
                                                                </Text>
                                                                :
                                                                <></>
                                                        }

                                                        <FlatList
                                                            horizontal={false}
                                                            nestedScrollEnabled={true}
                                                            data={outletsItemAddOnChoiceDict[item.uniqueId] ? outletsItemAddOnChoiceDict[item.uniqueId].filter(choiceFilter => {
                                                                return !choiceFilter.isHidden;
                                                            }) : []}
                                                            renderItem={dataItem => renderAddons(dataItem, item)}
                                                            style={{
                                                                marginTop: 10,
                                                            }}
                                                        />
                                                    </View>
                                                );
                                            }
                                        })
                                        : null}
                                </>
                            }
                            <View style={{
                                marginBottom: 15,
                                marginTop: 5,

                                opacity: (selectedOutlet && (
                                    selectedOutlet.rmShowM === undefined
                                    ||
                                    selectedOutlet.rmShowM === true
                                )) ? 100 : 0,
                            }}>
                                <Text
                                    style={{
                                        color: Colors.descriptionColor,
                                        // color: Colors.mainTxtColor,
                                        // fontWeight: "500",
                                        fontSize: 17,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        marginBottom: 15,
                                    }}
                                >
                                    Special Remarks:
                                </Text>
                                <TextInput
                                    testID="remarks"
                                    underlineColorAndroid={Colors.fieldtBgColor}
                                    style={[styles.textInput, {
                                        height: windowHeight * 0.1
                                    }]}
                                    placeholder="eg: no onions"
                                    onChangeText={(text) => {
                                        // setState({ remark: text });
                                        setRemark(text);
                                    }}
                                    value={remark}
                                    multiline={true}
                                />
                            </View>
                        </View>
                    </ScrollView>

                    <View style={{
                        position: "absolute",
                        bottom: insets.bottom,
                        // bottom: 'env(safe-area-inset-bottom)',
                        left: 0,
                        right: 0,
                        flexDirection: 'row',
                        backgroundColor: Colors.whiteColor,
                        width: windowWidth,
                        height: 60,
                        alignItems: 'center',
                        paddingHorizontal: 20,
                        paddingVertical: 7,
                        justifyContent: 'space-between',
                    }}>
                        <View style={{ flexDirection: "row", borderWidth: 1, borderRadius: 25, borderColor: Colors.primaryColor, alignItems: 'center', width: '25%', justifyContent: 'space-around' }}>
                            <TouchableOpacity
                                onPress={() => {
                                    if (global.outletName) {
                                        logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_QUANTITY_CLICK, {
                                            eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_QUANTITY_CLICK,

                                            outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                            webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                        });
                                    }

                                    setModalQuantity(modalQuantity - 1 >= 1 ? modalQuantity - 1 : 1);
                                }}>
                                <View
                                    testID="menuItemDetailMinus"
                                    style={[
                                        styles.addBtn,
                                        // { width: windowWidth * 0.075, },
                                    ]}
                                >
                                    <FontAwesome name="minus"
                                        color={Colors.primaryColor}
                                        size={isMobile() ? 12 : 12}
                                    />
                                </View>
                            </TouchableOpacity>
                            <View
                                style={[
                                    styles.addBtn,
                                    // { width: windowWidth * 0.075, },
                                ]}
                            >
                                <Text
                                    testID="menuItemDetailQuantity"
                                    style={{
                                        fontSize: 18,
                                        fontFamily: "NunitoSans-Bold",
                                        color: Colors.primaryColor,
                                        marginBottom: isMobile() ? 0 : 3,
                                    }}>
                                    {modalQuantity}
                                </Text>
                            </View>

                            <TouchableOpacity
                                onPress={() => {
                                    if (global.outletName) {
                                        logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_QUANTITY_CLICK, {
                                            eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_QUANTITY_CLICK,

                                            outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                            webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                        });
                                    }

                                    setModalQuantity(modalQuantity + 1);
                                }}
                                style={{}}
                            >
                                <View
                                    testID="menuItemDetailPlus"
                                    style={[
                                        styles.addBtn,
                                        {
                                            // left: -1,
                                            // width: windowWidth * 0.075,
                                        },
                                    ]}
                                >
                                    <FontAwesome name="plus"
                                        color={Colors.primaryColor}
                                        size={isMobile() ? 12 : 12}
                                    />
                                </View>
                            </TouchableOpacity>
                        </View>

                        <View style={{ width: '70%', backgroundColor: Colors.primaryColor, alignItems: 'center', borderRadius: 20, }}>
                            <TouchableOpacity
                                onPress={async () => {
                                    if (global.outletName) {
                                        logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_ADD_CLICK, {
                                            eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_ADD_CLICK,

                                            outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                            webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                        });
                                    }

                                    if (addOnVerified && addOnMinMaxMessage.length <= 0 && !isLoading) {
                                        if (!isOrdering) {
                                            CommonStore.update((s) => {
                                                s.isOrdering = true;
                                                s.isLoading = true;
                                            });

                                            var newCartItems = [];

                                            if (selectedOutletItem) {
                                                var tempCartItemChoices = {};

                                                if (outletsItemAddOnDict[selectedOutletItem.uniqueId]) {
                                                    // const tempOutletsItemAddOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId];

                                                    let tempOutletsItemAddOnList =
                                                        [...outletsItemAddOnDict[selectedOutletItem.uniqueId]];

                                                    tempOutletsItemAddOnList.sort((a, b) => {
                                                        return (
                                                            ((a.orderIndex !== undefined)
                                                                ? a.orderIndex
                                                                : tempOutletsItemAddOnList.length) -
                                                            ((b.orderIndex !== undefined)
                                                                ? b.orderIndex
                                                                : tempOutletsItemAddOnList.length)
                                                        );
                                                    })

                                                    for (var i = 0; i < tempOutletsItemAddOnList.length; i++) {
                                                        // check against the default item add on list

                                                        const tempAddOn = tempOutletsItemAddOnList[i];

                                                        if (selectedOutletItemAddOn[tempAddOn.uniqueId] !== undefined) {
                                                            // means this addon got choices selected before (could also means deselected, so need checks further as shown below)

                                                            const tempAddOnSelectedObjList = Object.entries(
                                                                selectedOutletItemAddOn[tempAddOn.uniqueId],
                                                            ).map(([key, value]) => ({ key: key, value: value }));

                                                            for (var j = 0; j < tempAddOnSelectedObjList.length; j++) {
                                                                if (tempAddOnSelectedObjList[j].value === true) {
                                                                    // means this addon's choice is selected

                                                                    tempCartItemChoices[tempAddOnSelectedObjList[j].key] = true;
                                                                }
                                                                else {
                                                                    tempCartItemChoices[tempAddOnSelectedObjList[j].key] = false;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }

                                                ///////////////////////////////////////////////////

                                                // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));
                                                const newSelectedOutletItemAddOnList = Object.entries(
                                                    newSelectedOutletItemAddOnDetails,
                                                ).map(([key, value]) => ({ key: key, value: value })).sort((a, b) => a.value.choiceName.localeCompare(b.value.choiceName));

                                                var addOnGroupList = [];

                                                for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
                                                    if (newSelectedOutletItemAddOnList[i].value &&
                                                        newSelectedOutletItemAddOnList[i].value.quantity > 0) {
                                                        const addOnTemp = newSelectedOutletItemAddOnDetails[newSelectedOutletItemAddOnList[i].key];

                                                        if (addOnTemp) {
                                                            addOnGroupList.push(addOnTemp);
                                                        }
                                                    }
                                                }

                                                ///////////////////////////////////////////////////

                                                if (onUpdatingCartItem) {
                                                    // update existing cart item

                                                    var updateCartItemIndex = 0;

                                                    for (var i = 0; i < cartItems.length; i++) {
                                                        if (cartItems[i].itemId === onUpdatingCartItem.itemId &&
                                                            cartItems[i].cartItemDate === onUpdatingCartItem.cartItemDate) {
                                                            updateCartItemIndex = i;
                                                        }
                                                    }

                                                    // var newCartItems = [];

                                                    if (modalQuantity <= 0) {
                                                        newCartItems = [
                                                            ...cartItems.slice(0, updateCartItemIndex),
                                                            ...cartItems.slice(updateCartItemIndex + 1),
                                                        ];
                                                    }
                                                    else {
                                                        ////////////////////////////////////////////////////////

                                                        // 2025-08-25 - tax settings

                                                        let tId = 'default';
                                                        let tRate = selectedOutlet.taxRate;
                                                        let tCode = selectedOutlet.taxCode ? selectedOutlet.taxCode : 'SST';
                                                        let tName = selectedOutlet.taxName ? selectedOutlet.taxName : 'SST';
                                                        let foundCustomTax = null;

                                                        foundCustomTax = outletCustomTaxList.find(customTax => customTax.uniqueId === selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId);

                                                        if (
                                                            selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] &&
                                                            selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId &&
                                                            selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId !== undefined &&
                                                            selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId !== 'default' &&
                                                            foundCustomTax
                                                        ) {
                                                            tId = foundCustomTax.uniqueId;
                                                            tRate = foundCustomTax.rate;
                                                            tCode = foundCustomTax.code;
                                                            tName = foundCustomTax.name;
                                                        }

                                                        ////////////////////////////////////////////////////////

                                                        newCartItems = [
                                                            ...cartItems.slice(0, updateCartItemIndex),
                                                            {
                                                                tId: tId,
                                                                tRate: tRate,
                                                                tCode: tCode,
                                                                tName: tName,

                                                                priceTemp: (selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) ? (parseFloat(variablePrice)) : ((totalPrice + addOnPrice) * modalQuantity),

                                                                itemId: selectedOutletItem.uniqueId,
                                                                choices: tempCartItemChoices,
                                                                remarks: remark,
                                                                fireOrder: false,
                                                                quantity: modalQuantity,
                                                                cartItemDate: Date.now(), // hmm need update or use existing?

                                                                addOnGroupList: addOnGroupList,

                                                                itemSku: selectedOutletItem.sku,
                                                                categoryId: selectedOutletItem.categoryId,
                                                                printerAreaList: selectedOutletItem.printerAreaList || [],
                                                                printingTypeList: selectedOutletItem.printingTypeList || null,

                                                                isDocket: selectedOutletItem.isDocket || false,
                                                                printDocketQuantity: selectedOutletItem.printDocketQuantity || 1,

                                                                ...(selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) && {
                                                                    priceVariable: parseFloat(variablePrice),
                                                                },

                                                                priceType: selectedOutletItem.priceType ? selectedOutletItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                                                                unitType: selectedOutletItem.unitType ? selectedOutletItem.unitType : UNIT_TYPE.GRAM,

                                                                itemCostPrice: selectedOutletItem.itemCostPrice ? selectedOutletItem.itemCostPrice : 0,

                                                                ...selectedOutletItem.upsellingCampaignId && {
                                                                    priceUpselling: selectedOutletItem.priceUpselling,
                                                                    upsellingCampaignId: selectedOutletItem.upsellingCampaignId,

                                                                    upc: selectedOutletItem.upc,
                                                                },
                                                            },
                                                            ...cartItems.slice(updateCartItemIndex + 1),
                                                        ];
                                                    }

                                                    if (newCartItems.length > 0) {
                                                        safelyExecuteIdb(() => {
                                                            // AsyncStorage.setItem(`${firebaseUid}.cartItems`, JSON.stringify(newCartItems));
                                                            idbSet(`cartItems`, JSON.stringify(newCartItems));

                                                            // AsyncStorage.setItem(`${firebaseUid}.cartOutletId`, selectedOutlet.uniqueId);
                                                            idbSet(`cartOutletId`, selectedOutlet.uniqueId);
                                                        });
                                                    }
                                                    else {
                                                        safelyExecuteIdb(() => {
                                                            // AsyncStorage.removeItem(`${firebaseUid}.cartItems`);
                                                            idbDel(`cartItems`);

                                                            // AsyncStorage.removeItem(`${firebaseUid}.cartOutletId`);
                                                            idbDel(`cartOutletId`);
                                                        });
                                                    }

                                                    CommonStore.update(s => {
                                                        // s.cartItems = new Set(cartItems).add(selectedOutletItem.uniqueId);
                                                        s.cartItems = newCartItems;

                                                        if (newCartItems.length <= 0) {
                                                            s.cartOutletId = null;
                                                        }
                                                    });

                                                    PaymentStore.update(s => {
                                                        s.cartItemsPayment = newCartItems;
                                                        s.outletIdPayment = selectedOutlet.uniqueId;
                                                        s.dateTimePayment = Date.now();
                                                        s.orderTypePayment = orderType;
                                                    });

                                                    console.log('cartItemsPayment');
                                                    console.log(newCartItems)
                                                    console.log('outletIdPayment');
                                                    console.log(selectedOutlet.uniqueId)
                                                    console.log('dateTimePayment');
                                                    console.log(Date.now())
                                                    console.log('orderTypePayment');
                                                    console.log(orderType)
                                                }
                                                else {
                                                    // add to cart

                                                    if (modalQuantity <= 0) {
                                                        window.confirm('Info\n\nPlease add one or more items');

                                                        return;
                                                    }

                                                    ////////////////////////////////////////////////////////

                                                    // 2025-08-25 - tax settings

                                                    let tId = 'default';
                                                    let tRate = selectedOutlet.taxRate;
                                                    let tCode = selectedOutlet.taxCode ? selectedOutlet.taxCode : 'SST';
                                                    let tName = selectedOutlet.taxName ? selectedOutlet.taxName : 'SST';
                                                    let foundCustomTax = null;

                                                    foundCustomTax = outletCustomTaxList.find(customTax => customTax.uniqueId === selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId);

                                                    if (
                                                        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] &&
                                                        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId &&
                                                        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId !== undefined &&
                                                        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId !== 'default' &&
                                                        foundCustomTax
                                                    ) {
                                                        tId = foundCustomTax.uniqueId;
                                                        tRate = foundCustomTax.rate;
                                                        tCode = foundCustomTax.code;
                                                        tName = foundCustomTax.name;
                                                    }

                                                    ////////////////////////////////////////////////////////

                                                    newCartItems = [
                                                        ...cartItems,
                                                        {
                                                            tId: tId,
                                                            tRate: tRate,
                                                            tCode: tCode,
                                                            tName: tName,

                                                            priceTemp: (selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) ? (parseFloat(variablePrice)) : ((totalPrice + addOnPrice) * modalQuantity),

                                                            itemId: selectedOutletItem.uniqueId,
                                                            choices: tempCartItemChoices,
                                                            remarks: remark,
                                                            fireOrder: false,
                                                            quantity: modalQuantity,
                                                            cartItemDate: Date.now(),

                                                            addOnGroupList: addOnGroupList,

                                                            itemSku: selectedOutletItem.sku,
                                                            categoryId: selectedOutletItem.categoryId,
                                                            printerAreaList: selectedOutletItem.printerAreaList || [],
                                                            printingTypeList: selectedOutletItem.printingTypeList || null,

                                                            isDocket: selectedOutletItem.isDocket || false,
                                                            printDocketQuantity: selectedOutletItem.printDocketQuantity || 1,

                                                            ...(selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) && {
                                                                priceVariable: parseFloat(variablePrice),
                                                            },

                                                            priceType: selectedOutletItem.priceType ? selectedOutletItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                                                            unitType: selectedOutletItem.unitType ? selectedOutletItem.unitType : UNIT_TYPE.GRAM,

                                                            itemCostPrice: selectedOutletItem.itemCostPrice ? selectedOutletItem.itemCostPrice : 0,

                                                            ...selectedOutletItem.upsellingCampaignId && {
                                                                priceUpselling: selectedOutletItem.priceUpselling,
                                                                upsellingCampaignId: selectedOutletItem.upsellingCampaignId,

                                                                upc: selectedOutletItem.upc,
                                                            },
                                                        },
                                                    ];

                                                    safelyExecuteIdb(() => {
                                                        // AsyncStorage.setItem(`${firebaseUid}.cartItems`, JSON.stringify(newCartItems));
                                                        idbSet(`cartItems`, JSON.stringify(newCartItems));
                                                    });

                                                    AsyncStorage.setItem(`${firebaseUid}.cartOutletId`, selectedOutlet.uniqueId);

                                                    CommonStore.update(s => {
                                                        s.cartItemChoices = {
                                                            ...cartItemChoices,
                                                            [selectedOutletItem.uniqueId]: {
                                                                ...tempCartItemChoices,
                                                            },
                                                        };

                                                        // s.cartItems = new Set(cartItems).add(selectedOutletItem.uniqueId);
                                                        s.cartItems = newCartItems;
                                                    });

                                                    PaymentStore.update(s => {
                                                        s.cartItemsPayment = newCartItems;
                                                        s.outletIdPayment = selectedOutlet.uniqueId;
                                                        s.dateTimePayment = Date.now();
                                                        s.orderTypePayment = orderType;
                                                    });

                                                    console.log('cartItemsPayment');
                                                    console.log(newCartItems)
                                                    console.log('outletIdPayment');
                                                    console.log(selectedOutlet.uniqueId)
                                                    console.log('dateTimePayment');
                                                    console.log(Date.now())
                                                    console.log('orderTypePayment');
                                                    console.log(orderType)

                                                    console.log('cartItemChoices');
                                                    console.log({
                                                        ...cartItemChoices,
                                                        [selectedOutletItem.uniqueId]: {
                                                            ...tempCartItemChoices,
                                                        },
                                                    });
                                                    console.log('cartItems');
                                                    console.log(newCartItems);
                                                }
                                            }

                                            if (selectedOutletTableId.length > 0) {
                                                await updateUserCart(newCartItems);
                                            }

                                            CommonStore.update(s => {
                                                s.cartOutletId = selectedOutlet.uniqueId;
                                            });

                                            if (onUpdatingCartItem) {

                                                const subdomain = await AsyncStorage.getItem('latestSubdomain');

                                                await AsyncStorage.setItem('onUpdatingCartItem', '1');

                                                if (!subdomain) {
                                                    linkTo && linkTo(`${prefix}/outlet/cart`);
                                                }
                                                else {
                                                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                                                }

                                                if (global.redirectFromPage === 'NoVoucher Cart') {
                                                    global.redirectFromPage = '';

                                                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/novoucher-cart`);
                                                }
                                                else {
                                                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                                                }

                                                CommonStore.update(s => {
                                                    s.currPageIframe = 'Cart';
                                                });

                                                // linkTo(`${prefix}/outlet/cart`);
                                            }
                                            else {
                                                if (global.isFromRecommendedItems) {
                                                    global.isFromRecommendedItems = false;

                                                    const subdomain = await AsyncStorage.getItem('latestSubdomain');

                                                    if (!subdomain) {
                                                        linkTo && linkTo(`${prefix}/outlet/cart`);
                                                    }
                                                    else {
                                                        linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                                                    }

                                                    CommonStore.update(s => {
                                                        s.currPageIframe = 'Cart';
                                                    });
                                                }
                                                else {
                                                    const subdomain = await AsyncStorage.getItem('latestSubdomain');

                                                    if (!subdomain) {
                                                        linkTo && linkTo(`${prefix}/outlet/menu`);

                                                        CommonStore.update(s => {
                                                            s.currPageIframe = 'OutletMenu';
                                                        });
                                                    }
                                                    else {
                                                        // if (subdomain === 'hominsan-ss15' || subdomain === 'hominsanttdi') {
                                                        if (true) {
                                                            if (
                                                                (upsellingCampaignsAfterCart && upsellingCampaignsAfterCart.length > 0)
                                                                ||
                                                                (global.upsellingCampaignsAfterCart && global.upsellingCampaignsAfterCart.length > 0)
                                                            ) {
                                                                linkTo && linkTo(`${prefix}/outlet/${subdomain}/upsell-menu`);

                                                                CommonStore.update((s) => {
                                                                    s.currPage = "UpsellMenu";
                                                                });

                                                                CommonStore.update(s => {
                                                                    s.currPageIframe = 'UpsellingAfterCart';
                                                                });
                                                            }
                                                            else {
                                                                if (global.redirectFromPage === 'Reservation') {
                                                                    global.redirectFromPage = '';

                                                                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation`);
                                                                }
                                                                else {
                                                                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);

                                                                    CommonStore.update(s => {
                                                                        s.currPageIframe = 'OutletMenu';
                                                                    });
                                                                }
                                                            }
                                                        }
                                                        else {
                                                            linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                                                        }
                                                    }

                                                    // linkTo(`${prefix}/outlet/menu`); 
                                                }
                                            }
                                            CommonStore.update((s) => {
                                                s.menuItemDetailModal = false;
                                            });

                                            global.menuItemDetailModal = false;

                                            setNewSelectedOutletItemAddOnDetails({});

                                            // navigation.goBack();

                                            setTimeout(() => {
                                                CommonStore.update((s) => {
                                                    s.isOrdering = false;
                                                    s.isLoading = false;
                                                });
                                            }, 1000);
                                        }
                                    }
                                    else {
                                        console.log(addOnVerifiedResultList);

                                        if (addOnVerifiedResultList.length > 0) {
                                            // variants checking

                                            let addOnVerifiedResult = addOnVerifiedResultList[0];
                                            if (typeof global.addOnPosYByIdDict[addOnVerifiedResult.uniqueId] === 'number') {
                                                setAddOnMinMaxMessageResultDict({});

                                                setAddOnVerifiedResultDict({
                                                    [addOnVerifiedResult.uniqueId]: true,
                                                });

                                                addOnScrollViewRef.current.scrollTo({ y: global.addOnPosYByIdDict[addOnVerifiedResult.uniqueId] - 50, animated: true });
                                            }
                                        }
                                        else if (addOnMinMaxMessageResultList.length > 0) {
                                            // addons checking

                                            let addOnMinMaxMessageResult = addOnMinMaxMessageResultList[0];
                                            if (typeof global.addOnPosYByIdDict[addOnMinMaxMessageResult.uniqueId] === 'number') {
                                                setAddOnVerifiedResultDict({});

                                                setAddOnMinMaxMessageResultDict({
                                                    [addOnMinMaxMessageResult.uniqueId]: true,
                                                });

                                                addOnScrollViewRef.current.scrollTo({ y: global.addOnPosYByIdDict[addOnMinMaxMessageResult.uniqueId] - 50, animated: true });
                                            }
                                        }

                                        alert(`Info, Please select your choice before proceed.\n\n${addOnMinMaxMessage}`)

                                        // CommonStore.update(s => {
                                        //   s.alertObj = {
                                        //     title: 'Info',
                                        //     message: `Please select your choice before proceed.\n\n${addOnMinMaxMessage}`,
                                        //   };
                                        // });
                                    }
                                }}>
                                <View style={{
                                    paddingVertical: 12, flexDirection: 'row', alignItems: 'center'
                                }}>
                                    <Text
                                        testID="addToCart"
                                        style={{
                                            color: "#ffffff",
                                            fontSize: 16,
                                            // fontWeight: "bold",
                                            fontFamily: 'NunitoSans-Regular',
                                            borderRightWidth: 1,
                                            borderColor: Colors.whiteColor,
                                            paddingRight: 7,
                                        }}
                                    >
                                        {onUpdatingCartItem ? 'Update' : 'Add'}
                                    </Text>
                                    <Text style={{
                                        color: "#ffffff",
                                        fontSize: 16,
                                        // fontWeight: "bold",
                                        fontFamily: 'NunitoSans-Bold',
                                        paddingLeft: 7,
                                    }}>
                                        {'RM '}{(
                                            ((selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) ? (parseFloat(variablePrice)) : ((totalPrice + addOnPrice) * modalQuantity))
                                        ).toFixed(2)}{selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` for ${modalQuantity}${UNIT_TYPE_SHORT[selectedOutletItem.unitType]}` : ''}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal >
        </SafeAreaProvider>
    )
}
const styles = StyleSheet.create({
    confirmBox: {
        width: 350,
        // height: windowHeight * 0.35,
        height: 320,
        borderRadius: 20,
        backgroundColor: Colors.whiteColor,
    },
    container: {
        // flex: 1,
        width: Dimensions.get("window").width,
        height: Dimensions.get("window").height,
        backgroundColor: "#ffffff",
        position: "relative",
    },
    outletCover: {
        // width: isMobile() ? '100%' : windowWidth,
        // alignSelf: 'center',
        // height: isMobile() ? undefined : windowHeight * 0.5,
        // aspectRatio: isMobile() ? 2 : 2,
        // resizeMode: isMobile() ? 'stretch' : 'stretch',
        width: isMobile() ? "100%" : "auto",
        alignSelf: "center",
        height: isMobile() ? undefined : Dimensions.get("window").height * 0.5,
        aspectRatio: isMobile() ? 2 : 2,
        resizeMode: isMobile() ? "stretch" : "stretch",
        ...(!isMobile() && {}),
    },
    infoTab: {
        backgroundColor: Colors.fieldtBgColor,
    },
    workingHourTab: {
        padding: 16,
        flexDirection: "row",
    },
    outletAddress: {
        textAlign: "center",
        color: Colors.mainTxtColor,
    },
    outletName: {
        fontWeight: "bold",
        fontSize: 20,
        marginBottom: 10,
    },
    logo: {
        width: 100,
        height: 100,
    },
    actionTab: {
        flexDirection: "row",
        marginTop: 20,
    },
    actionView: {
        width: Dimensions.get("window").width / 4,
        height: Dimensions.get("window").height / 4,
        justifyContent: "center",
        alignItems: "center",
        alignContent: "center",
    },
    actionBtn: {
        borderRadius: 50,
        width: 70,
        height: 70,
        borderColor: Colors.secondaryColor,
        borderWidth: StyleSheet.hairlineWidth,
        justifyContent: "center",
        alignItems: "center",
    },
    actionText: {
        fontSize: 12,
        marginTop: 10,
    },
    category: {
        // width: 150,
        paddingHorizontal: 25,
        justifyContent: "center",
        alignItems: "center",
    },
    floatCartBtn: {
        zIndex: 2,
        position: "absolute",
        bottom: 30,
        right: 30,
        width: 60,
        height: 60,
        borderRadius: 60 / 2,
        backgroundColor: Colors.secondaryColor,
        justifyContent: "center",
        alignContent: "center",
        alignItems: "center",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    cartCount: {
        position: "absolute",
        top: -8,
        right: 5,
        backgroundColor: Colors.primaryColor,
        width: 20,
        height: 20,
        borderRadius: 20 / 2,
        alignContent: "center",
        alignItems: "center",
        justifyContent: "center",
    },
    addBtn: {
        // width: 20,
        height: 45,

        display: 'flex',
        justifyContent: "center",
        alignItems: "center",

        borderColor: 'transparent',
    },
    textInput: {
        // height: 100,
        // height: Dimensions.get('window').width * 0.1,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 12,

        fontSize: 14,
        fontFamily: 'NunitoSans-Regular',
        // color: Colors.descriptionColor,
        textAlignVertical: 'top',
        paddingVertical: 15,
        borderWidth: 0.5,
        opacity: 0.7,
    },
});
export default MenuItemDetailModal;
